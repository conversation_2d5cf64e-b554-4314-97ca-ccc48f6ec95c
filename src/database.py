"""
数据库连接和查询模块
实现MySQL数据库连接，创建发布计划查询功能
"""

import mysql.connector
from typing import List, Dict, Any, Optional
import os
from dotenv import load_dotenv
import logging
import time

load_dotenv()

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器，负责连接和查询操作"""
    
    def __init__(self):
        self.connection = None
        self.connection_time = None
        self.max_connection_age = 3600  # 1小时后强制重连
        self.connect()
    
    def connect(self):
        """建立数据库连接"""
        try:
            # 为长主机名创建自定义配置，避免连接池名称过长
            connection_config = {
                'host': os.getenv('MYSQL_HOST', 'localhost'),
                'port': int(os.getenv('MYSQL_PORT', 3306)),
                'user': os.getenv('MYSQL_USER'),
                'password': os.getenv('MYSQL_PASSWORD'),
                'database': os.getenv('MYSQL_DATABASE'),
                'charset': 'utf8mb4',
                'autocommit': True,
                'use_unicode': True,
                'sql_mode': 'TRADITIONAL',
                'raise_on_warnings': True
            }
            
            # 如果主机名过长，使用简化的连接池配置
            host = connection_config['host']
            if len(host) > 50:  # 如果主机名太长
                logger.info(f"检测到长主机名 ({len(host)} 字符)，使用简化连接配置")
                # 创建不使用连接池的连接，避免名称过长问题
                self.connection = mysql.connector.connect(**connection_config)
            else:
                # 使用连接池
                connection_config.update({
                    'pool_reset_session': True,
                    'pool_name': 'release_pool',
                    'pool_size': 5
                })
                self.connection = mysql.connector.connect(**connection_config)
            
            self.connection_time = time.time()
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            self.connection = None
            self.connection_time = None
            raise
    
    def is_connection_valid(self) -> bool:
        """检查数据库连接是否有效"""
        try:
            if not self.connection:
                return False
            
            if not self.connection.is_connected():
                return False
            
            # 检查连接年龄
            if self.connection_time and (time.time() - self.connection_time) > self.max_connection_age:
                logger.info("数据库连接已超时，需要重新连接")
                return False
            
            # 发送ping测试连接
            self.connection.ping(reconnect=False)
            return True
            
        except Exception as e:
            logger.warning(f"数据库连接无效: {e}")
            return False
    
    def ensure_connection(self):
        """确保数据库连接有效，如果无效则重新连接"""
        if not self.is_connection_valid():
            logger.info("重新建立数据库连接")
            try:
                if self.connection:
                    self.connection.close()
            except:
                pass
            self.connect()
    
    def force_reconnect(self):
        """强制重新连接数据库"""
        logger.info("强制重新连接数据库")
        try:
            if self.connection:
                self.connection.close()
        except:
            pass
        self.connection = None
        self.connection_time = None
        self.connect()
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        try:
            # 确保连接有效
            self.ensure_connection()

            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            results = cursor.fetchall()
            cursor.close()
            logger.debug(f"查询执行成功，返回 {len(results)} 条记录")
            return results
            
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            # 尝试重新连接一次
            try:
                logger.info("尝试重新连接数据库")
                self.force_reconnect()
                cursor = self.connection.cursor(dictionary=True)
                cursor.execute(query, params or ())
                results = cursor.fetchall()
                cursor.close()
                logger.debug(f"重试查询成功，返回 {len(results)} 条记录")
                return results
            except Exception as retry_e:
                logger.error(f"重试查询也失败: {retry_e}")
                raise
    
    def get_multi_tenant_services(self, version: str, environment: str) -> List[Dict[str, Any]]:
        """查询多租户服务"""
        query = """
        SELECT 
            s.name AS service_name,
            s.deployment_order
        FROM 
            release_version rv
        JOIN 
            release_service rs ON rv.id = rs.release_version_id
        JOIN 
            service s ON rs.service_id = s.id
        JOIN 
            environment e ON rs.environment_id = e.id
        WHERE 
            rv.version = %s
            AND e.name = %s
            AND s.is_multi_tenant = 1
        ORDER BY
            s.deployment_order ASC;
        """
        return self.execute_query(query, (version, environment))
    
    def get_single_tenant_deployment_plan(self, version: str, environment: str) -> List[Dict[str, Any]]:
        """查询单租户服务部署计划"""
        query = """
        SELECT
            `计划部署日期`,
            `时间窗口`,
            `客户名`,
            `租户名`,
            `Service名`,
            `是否部署PS代码`,
            `部署顺序`
        FROM
            deployment_plan_view
        WHERE
            `版本号` = %s AND `环境名` = %s
            AND `Service名` NOT IN (SELECT s.name FROM service s WHERE s.is_multi_tenant = 1)
        ORDER BY
            `计划部署日期` ASC, `时间窗口` ASC,
            CASE `客户状态` 
              WHEN '内部' THEN 1
              WHEN '准生产' THEN 2
              WHEN '已上线' THEN 3
              ELSE 4
            END ASC,
            `部署顺序` ASC,
            `客户ID` ASC;
        """
        return self.execute_query(query, (version, environment))
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

class ReleaseQueryService:
    """发布计划查询服务"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def force_refresh_connection(self):
        """强制刷新数据库连接"""
        logger.info("强制刷新数据库连接")
        self.db.force_reconnect()
    
    def get_deployment_plan(self, version: str, environment: str) -> Dict[str, Any]:
        """获取完整的部署计划"""
        try:
            # 查询多租户服务
            multi_tenant_services = self.db.get_multi_tenant_services(version, environment)
            
            # 查询单租户服务部署计划
            single_tenant_plan = self.db.get_single_tenant_deployment_plan(version, environment)
            
            if not multi_tenant_services and not single_tenant_plan:
                return {
                    "success": False,
                    "message": f"未找到 {version} 在 {environment} 环境的发布计划。",
                    "data": None
                }
            
            # 整合发布计划
            integrated_plan = self._integrate_deployment_plan(
                multi_tenant_services, single_tenant_plan, version, environment
            )
            
            return {
                "success": True,
                "message": f"{version} {environment}环境 发布计划",
                "data": integrated_plan
            }
            
        except Exception as e:
            logger.error(f"获取部署计划失败: {e}")
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": None
            }
    
    def _integrate_deployment_plan(self, multi_tenant_services: List[Dict], 
                                 single_tenant_plan: List[Dict], 
                                 version: str, environment: str) -> List[Dict]:
        """整合多租户服务和单租户服务的部署计划"""
        integrated_plan = []
        
        if not single_tenant_plan:
            # 如果没有单租户服务，直接返回多租户服务
            for service in multi_tenant_services:
                integrated_plan.append({
                    "计划部署日期": "",
                    "时间窗口": "",
                    "客户名": "多租户服务",
                    "租户名": "",
                    "Service名": service["service_name"],
                    "是否部署PS代码": "",
                    "部署顺序": service["deployment_order"]
                })
            return integrated_plan
        
        # 获取单租户服务的部署顺序范围
        single_tenant_orders = [item["部署顺序"] for item in single_tenant_plan if item["部署顺序"]]
        min_order = min(single_tenant_orders) if single_tenant_orders else float('inf')
        max_order = max(single_tenant_orders) if single_tenant_orders else float('-inf')
        
        # 获取第一批客户的发布日期和时间窗口
        first_batch_date = single_tenant_plan[0]["计划部署日期"] if single_tenant_plan else ""
        first_batch_items = [item for item in single_tenant_plan if item["计划部署日期"] == first_batch_date]
        last_time_window = max([item["时间窗口"] for item in first_batch_items]) if first_batch_items else ""
        
        # 分类多租户服务
        pre_services = [s for s in multi_tenant_services if s["deployment_order"] < min_order]
        post_services = [s for s in multi_tenant_services if s["deployment_order"] > max_order]
        
        # 添加pre多租户服务
        for service in pre_services:
            integrated_plan.append({
                "计划部署日期": first_batch_date,
                "时间窗口": first_batch_items[0]["时间窗口"] if first_batch_items else "",
                "客户名": "多租户服务",
                "租户名": "",
                "Service名": service["service_name"],
                "是否部署PS代码": "",
                "部署顺序": service["deployment_order"]
            })
        
        # 添加第一批客户服务
        for item in first_batch_items:
            integrated_plan.append(item)
        
        # 添加post多租户服务
        for service in post_services:
            integrated_plan.append({
                "计划部署日期": first_batch_date,
                "时间窗口": last_time_window,
                "客户名": "多租户服务",
                "租户名": "",
                "Service名": service["service_name"],
                "是否部署PS代码": "",
                "部署顺序": service["deployment_order"]
            })
        
        # 添加后续批次客户服务
        subsequent_batches = [item for item in single_tenant_plan if item["计划部署日期"] != first_batch_date]
        for item in subsequent_batches:
            integrated_plan.append(item)
        
        return integrated_plan
    
    def __del__(self):
        """析构函数，确保数据库连接关闭"""
        if hasattr(self, 'db'):
            self.db.close()
