import streamlit as st
from datetime import datetime
from typing import Dict, List

def parse_deployment_plan_table(deployment_plan_text: str):
    """解析发布计划表格文本"""
    lines = deployment_plan_text.strip().split('\n')
    plan_items = []

    # 找到表格开始位置
    table_start = -1
    for i, line in enumerate(lines):
        if '| 计划部署日期 |' in line:
            table_start = i + 2  # 跳过表头和分隔线
            break

    if table_start == -1:
        return []

    # 解析表格行
    for line in lines[table_start:]:
        line = line.strip()
        if not line or not line.startswith('|'):
            continue

        # 分割表格列
        columns = [col.strip() for col in line.split('|')[1:-1]]

        if len(columns) >= 6:
            plan_items.append({
                '计划部署日期': columns[0],
                '时间窗口': columns[1],
                '客户名': columns[2],
                '租户名': columns[3],
                'Service名': columns[4],
                '是否部署PS代码': columns[5]
            })

    return plan_items

def parse_task_datetime(item: dict):
    """解析任务的日期时间"""
    try:
        date_str = item.get('计划部署日期', '').strip()
        time_str = item.get('时间窗口', '').strip()

        if not date_str or not time_str:
            return None

        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

        if '-' in time_str:
            start_time_str = time_str.split('-')[0].strip()
        else:
            start_time_str = time_str

        time_obj = datetime.strptime(start_time_str, '%H:%M').time()

        return datetime.combine(date_obj, time_obj)

    except (ValueError, IndexError):
        return None

def analyze_job_time_windows(jobs: list, current_time: datetime):
    """分析所有jobs的时间窗口状态"""
    analysis = {
        "deployable_jobs": [],
        "future_jobs": [],
        "expired_jobs": [],
        "no_time_jobs": []
    }

    for i, job in enumerate(jobs):
        deployment_date = job.get('deployment_date', '')
        time_window = job.get('time_window', '')

        time_status = parse_job_time_status(deployment_date, time_window, current_time)

        job_info = {
            "job": job,
            "index": i,
            "time_status": time_status
        }

        if time_status["status"] == "in_time":
            analysis["deployable_jobs"].append(job_info)
        elif time_status["status"] == "future":
            analysis["future_jobs"].append(job_info)
        elif time_status["status"] == "past":
            analysis["expired_jobs"].append(job_info)
        else:  # no_time
            analysis["no_time_jobs"].append(job_info)

    return analysis

def parse_job_time_status(deployment_date: str, time_window: str, current_time: datetime):
    """解析job的时间状态"""
    try:
        if not deployment_date or not time_window:
            return {"status": "no_time"}

        job_date = datetime.strptime(deployment_date, '%Y-%m-%d').date()
        start_time_str, end_time_str = time_window.split('-')
        start_time = datetime.strptime(start_time_str.strip(), '%H:%M').time()
        end_time = datetime.strptime(end_time_str.strip(), '%H:%M').time()

        start_datetime = datetime.combine(job_date, start_time)
        end_datetime = datetime.combine(job_date, end_time)

        if current_time < start_datetime:
            return {"status": "future", "time_until_start": format_time_diff(start_datetime - current_time)}
        elif current_time > end_datetime:
            return {"status": "past", "time_since_end": format_time_diff(current_time - end_datetime)}
        else:
            return {"status": "in_time", "time_remaining": format_time_diff(end_datetime - current_time)}

    except (ValueError, IndexError):
        return {"status": "no_time"}

def format_time_diff(time_diff):
    """格式化时间差"""
    total_seconds = int(time_diff.total_seconds())

    if total_seconds < 0:
        return "已过期"

    days = total_seconds // 86400
    hours = (total_seconds % 86400) // 3600
    minutes = (total_seconds % 3600) // 60

    if days > 0:
        return f"{days}天{hours}小时"
    elif hours > 0:
        return f"{hours}小时{minutes}分钟"
    else:
        return f"{minutes}分钟"

def display_time_analysis_summary_enhanced(time_analysis: dict, current_time: datetime):
    """增强版时间分析摘要显示"""
    st.subheader("⏰ 时间窗口分析")

    st.markdown(f"""
    <div style="padding: 10px; background-color: #f0f2f6; border-radius: 5px; margin-bottom: 15px;">
        <strong>📅 当前系统时间:</strong> {current_time.strftime('%Y-%m-%d %H:%M:%S')}
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    deployable_count = len(time_analysis["deployable_jobs"])
    future_count = len(time_analysis["future_jobs"])
    expired_count = len(time_analysis["expired_jobs"])

    col1.metric("🟢 可部署", f"{deployable_count} 个")
    col2.metric("🟡 未到时间", f"{future_count} 个")
    col3.metric("🔴 已过期", f"{expired_count} 个")
    col4.metric("⚪ 无时间限制", f"{len(time_analysis['no_time_jobs'])} 个")

    if deployable_count > 0:
        st.success(f"✅ **推荐执行**: 当前有 {deployable_count} 个Jobs符合部署时间条件，已自动勾选。")
    if future_count > 0:
        st.info(f"ℹ️ 有 {future_count} 个Jobs将在未来执行。")
    if expired_count > 0:
        st.error(f"⚠️ **注意**: 有 {expired_count} 个Jobs已错过部署时间窗口。")

def add_test_time_data(jobs: list):
    """为演示目的，为jobs添加测试用的时间数据"""
    from datetime import timedelta

    now = datetime.now()
    for i, job in enumerate(jobs):
        if i % 4 == 0:
            # 可部署
            job['deployment_date'] = now.strftime('%Y-%m-%d')
            start = now - timedelta(minutes=30)
            end = now + timedelta(minutes=30)
            job['time_window'] = f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
        elif i % 4 == 1:
            # 未来
            start = now + timedelta(hours=1)
            end = now + timedelta(hours=2)
            job['deployment_date'] = start.strftime('%Y-%m-%d')
            job['time_window'] = f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
        elif i % 4 == 2:
            # 已过期
            start = now - timedelta(hours=2)
            end = now - timedelta(hours=1)
            job['deployment_date'] = start.strftime('%Y-%m-%d')
            job['time_window'] = f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
        else:
            # 无时间
            job['deployment_date'] = ''
            job['time_window'] = ''
    return jobs

def display_smart_execution_status(group_status: Dict, execution_summary: Dict):
    """显示智能执行管理器的分组状态"""
    if not group_status:
        st.info("暂无执行组状态信息")
        return
    
    # 显示总体统计
    col1, col2, col3, col4 = st.columns(4)
    col1.metric("总Jobs", execution_summary.get("total_jobs", 0))
    col2.metric("已完成", execution_summary.get("completed_jobs", 0))
    col3.metric("运行中", execution_summary.get("running_jobs", 0)) 
    col4.metric("失败", execution_summary.get("failed_jobs", 0))
    
    # 进度条
    if execution_summary.get("total_jobs", 0) > 0:
        progress = (execution_summary.get("completed_jobs", 0) + execution_summary.get("failed_jobs", 0)) / execution_summary.get("total_jobs", 1)
        st.progress(progress)
    
    st.markdown("---")
    
    # 按执行组显示状态
    for group_id, group_info in group_status.items():
        display_name = group_info.get("display_name", group_id)
        is_completed = group_info.get("is_completed", False)
        current_job = group_info.get("current_job")
        current_job_status = group_info.get("current_job_status")
        
        # 确定组状态颜色
        if is_completed:
            status_icon = "✅"
            status_color = "green"
        elif current_job_status and current_job_status.value == "RUNNING":
            status_icon = "🔄"
            status_color = "blue"
        elif current_job_status and current_job_status.value == "FAILURE":
            status_icon = "❌"
            status_color = "red"
        else:
            status_icon = "⏳"
            status_color = "orange"
        
        with st.expander(f"{status_icon} **{display_name}** ({group_info.get('completed_jobs', 0)}/{group_info.get('total_jobs', 0)})", expanded=not is_completed):
            # 显示当前执行的Job
            if current_job and not is_completed:
                st.markdown(f"**当前执行:** {current_job}")
                if current_job_status:
                    status_value = current_job_status.value if hasattr(current_job_status, 'value') else str(current_job_status)
                    st.markdown(f"**状态:** :{status_color}[{status_value}]")
            
            # 显示Jobs详情表格
            jobs_detail = group_info.get("jobs_detail", [])
            if jobs_detail:
                jobs_df_data = []
                for job_detail in jobs_detail:
                    status = job_detail.get("status", "PENDING")
                    status_display = {
                        "PENDING": "⏳ 等待中",
                        "RUNNING": "🔄 运行中", 
                        "SUCCESS": "✅ 成功",
                        "FAILURE": "❌ 失败"
                    }.get(status, status)
                    
                    jobs_df_data.append({
                        "Job名称": job_detail.get("job_name", "N/A"),
                        "部署顺序": job_detail.get("deployment_order", "N/A"),
                        "状态": status_display
                    })
                
                st.dataframe(jobs_df_data, use_container_width=True)
    
    # 显示执行日志
    if execution_summary.get("execution_log"):
        with st.expander("📋 **执行日志**", expanded=False):
            log_lines = execution_summary.get("execution_log", [])
            # 只显示最新的20条日志
            recent_logs = log_lines[-20:] if len(log_lines) > 20 else log_lines
            for log_line in recent_logs:
                st.text(log_line)

def display_execution_order_info(jobs: List[Dict]):
    """显示执行顺序信息"""
    from collections import defaultdict
    
    # 按客户分组
    customer_groups = defaultdict(list)
    for job in jobs:
        customer_name = job.get("customer_name", "Unknown")
        tenant_name = job.get("tenant_name", "")
        
        if customer_name == "多租户服务":
            group_key = f"多租户服务({job.get('service_name', 'unknown')})"
        else:
            group_key = f"{customer_name}({tenant_name})" if tenant_name else customer_name
        
        customer_groups[group_key].append(job)
    
    st.markdown("### 📋 执行顺序规划")
    st.info("💡 **智能执行策略**: 同一客户的服务将按部署顺序串行执行，不同客户之间可以并行执行")
    
    for group_name, group_jobs in customer_groups.items():
        # 按部署顺序排序
        sorted_jobs = sorted(group_jobs, key=lambda x: x.get("deployment_order", 999))
        
        with st.expander(f"**{group_name}** ({len(sorted_jobs)} 个Jobs)", expanded=True):
            st.markdown("**执行顺序:**")
            for i, job in enumerate(sorted_jobs, 1):
                order = job.get("deployment_order", "N/A")
                job_name = job.get("job_name", "N/A")
                service_name = job.get("service_name", "N/A")
                
                if i == 1:
                    st.markdown(f"1️⃣ **{job_name}** (服务: {service_name}, 顺序: {order}) - 首先执行")
                else:
                    st.markdown(f"{i}️⃣ **{job_name}** (服务: {service_name}, 顺序: {order}) - 等待前序Job完成")
            
            if len(sorted_jobs) > 1:
                st.warning(f"⚠️ 该组内 {len(sorted_jobs)} 个Jobs将按上述顺序串行执行")
            else:
                st.success("✅ 该组只有1个Job，无需等待")

def display_combined_execution_info(jobs: List[Dict]):
    """合并显示执行顺序规划和Jobs参数信息"""
    from collections import defaultdict
    import streamlit as st
    
    # 按客户分组
    customer_groups = defaultdict(list)
    for job in jobs:
        customer_name = job.get("customer_name", "Unknown")
        tenant_name = job.get("tenant_name", "")
        
        if customer_name == "多租户服务":
            group_key = f"多租户服务({job.get('service_name', 'unknown')})"
        else:
            group_key = f"{customer_name}({tenant_name})" if tenant_name else customer_name
        
        customer_groups[group_key].append(job)
    
    st.markdown("### 📋 执行顺序规划与Jobs参数")
    st.info("💡 **智能执行策略**: 同一客户的服务将按部署顺序串行执行，不同客户之间可以并行执行")
    
    for group_name, group_jobs in customer_groups.items():
        # 按部署顺序排序
        sorted_jobs = sorted(group_jobs, key=lambda x: x.get("deployment_order", 999))
        
        with st.expander(f"**{group_name}** ({len(sorted_jobs)} 个Jobs)", expanded=True):
            st.markdown("**执行顺序与详细参数:**")
            
            for i, job in enumerate(sorted_jobs, 1):
                order = job.get("deployment_order", "N/A")
                job_name = job.get("job_name", "N/A")
                service_name = job.get("service_name", "N/A")
                
                # 确定执行顺序描述
                if i == 1:
                    execution_note = "首先执行"
                    order_icon = "1️⃣"
                else:
                    execution_note = "等待前序Job完成"
                    order_icon = f"{i}️⃣"
                
                # 使用嵌套的expander显示每个Job的详细信息
                with st.expander(f"{order_icon} **{job_name}** (服务: {service_name}, 顺序: {order}) - {execution_note}", expanded=True):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write("**基本信息:**")
                        st.write(f"- 客户: {job.get('customer_name', 'N/A')}")
                        st.write(f"- 服务: {job.get('service_name', 'N/A')}")
                        st.write(f"- 部署时间: {job.get('deployment_date', 'N/A')} {job.get('time_window', 'N/A')}")
                        st.write(f"- 部署顺序: {order}")
                    
                    with col2:
                        st.write("**Jenkins参数:**")
                        parameters = job.get('parameters', {})
                        if isinstance(parameters, dict) and parameters:
                            for key, value in parameters.items():
                                st.write(f"- {key}: `{value}`")
                        else:
                            st.write("- 无特殊参数")
            
            # 显示执行策略说明
            if len(sorted_jobs) > 1:
                st.warning(f"⚠️ 该组内 {len(sorted_jobs)} 个Jobs将按上述顺序串行执行")
            else:
                st.success("✅ 该组只有1个Job，无需等待")

def display_combined_execution_info_with_status(jobs: List[Dict], execution_status: Dict):
    """合并显示执行顺序规划、Jobs参数和执行状态信息"""
    from collections import defaultdict
    import streamlit as st
    from src.utils import get_status_color
    
    # 按客户分组
    customer_groups = defaultdict(list)
    for job in jobs:
        customer_name = job.get("customer_name", "Unknown")
        tenant_name = job.get("tenant_name", "")
        
        if customer_name == "多租户服务":
            group_key = f"多租户服务({job.get('service_name', 'unknown')})"
        else:
            group_key = f"{customer_name}({tenant_name})" if tenant_name else customer_name
        
        customer_groups[group_key].append(job)
    
    st.markdown("**已确认执行的Jobs执行顺序与参数:**")
    
    for group_name, group_jobs in customer_groups.items():
        # 按部署顺序排序
        sorted_jobs = sorted(group_jobs, key=lambda x: x.get("deployment_order", 999))
        
        with st.expander(f"**{group_name}** ({len(sorted_jobs)} 个Jobs)", expanded=False):
            st.markdown("**执行顺序与详细参数:**")
            
            for i, job in enumerate(sorted_jobs, 1):
                order = job.get("deployment_order", "N/A")
                job_name = job.get("job_name", "N/A")
                service_name = job.get("service_name", "N/A")
                
                # 确定执行顺序描述
                if i == 1:
                    execution_note = "首先执行"
                    order_icon = "1️⃣"
                else:
                    execution_note = "等待前序Job完成"
                    order_icon = f"{i}️⃣"
                
                # 使用嵌套的expander显示每个Job的详细信息
                with st.expander(f"{order_icon} **{job_name}** (服务: {service_name}, 顺序: {order}) - {execution_note}", expanded=False):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write("**基本信息:**")
                        st.write(f"- 客户: {job.get('customer_name', 'N/A')}")
                        st.write(f"- 服务: {job.get('service_name', 'N/A')}")
                        st.write(f"- 部署时间: {job.get('deployment_date', 'N/A')} {job.get('time_window', 'N/A')}")
                        st.write(f"- 部署顺序: {order}")
                        
                        # 显示当前执行状态
                        if job_name in execution_status:
                            job_status = execution_status[job_name].get('status', 'UNKNOWN')
                            status_color = get_status_color(job_status)
                            st.markdown(f"- 当前状态: <span style='color: {status_color}'>**{job_status}**</span>", 
                                      unsafe_allow_html=True)
                    
                    with col2:
                        st.write("**Jenkins参数:**")
                        parameters = job.get('parameters', {})
                        if isinstance(parameters, dict) and parameters:
                            for key, value in parameters.items():
                                st.write(f"- {key}: `{value}`")
                        else:
                            st.write("- 无特殊参数")
            
            # 显示执行策略说明
            if len(sorted_jobs) > 1:
                st.warning(f"⚠️ 该组内 {len(sorted_jobs)} 个Jobs已按上述顺序串行执行")
            else:
                st.success("✅ 该组只有1个Job，已完成执行")