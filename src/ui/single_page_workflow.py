"""
单页面工作流
将整个发布流程集成在一个页面中，以流的形式进行
"""

import streamlit as st
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from src.workflow_manager import workflow_manager
from src.ui.components import (
    parse_deployment_plan_table,
    analyze_job_time_windows,
    display_time_analysis_summary_enhanced,
    display_smart_execution_status,
    display_combined_execution_info,
    display_combined_execution_info_with_status
)
from src.constants import ENVIRONMENTS, ERROR_MESSAGES, SUCCESS_MESSAGES
from src.utils import get_status_color, format_job_display_name

def display_step_header(step_number: int, title: str, icon: str, description: str, is_current: bool = False):
    """显示步骤标题"""
    if is_current:
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            color: white;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        ">
            <h2 style="margin: 0; color: white;">
                步骤 {step_number}: {icon} {title}
            </h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">
                {description}
            </p>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div style="
            background: #f0f2f6;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        ">
            <h3 style="margin: 0; color: #333;">
                步骤 {step_number}: {icon} {title}
            </h3>
            <p style="margin: 5px 0 0 0; color: #666;">
                {description}
            </p>
        </div>
        """, unsafe_allow_html=True)

def step_1_plan_query():
    """步骤1: 计划查询"""
    current_step = workflow_manager.get_current_step()
    is_current = current_step == "plan_query"
    
    display_step_header(1, "计划查询", "📝", "查询和生成发布计划", is_current)
    
    # 如果不是当前步骤且已完成，使用折叠展示
    if not is_current and 'deployment_plan' in st.session_state:
        with st.expander("✅ 计划查询已完成 - 点击查看详情", expanded=False):
            deployment_plan = st.session_state.deployment_plan
            col1, col2 = st.columns(2)
            col1.metric("版本", deployment_plan.get("version", "N/A"))
            col2.metric("环境", deployment_plan.get("environment", "N/A"))
            
            if deployment_plan.get("deployment_plan"):
                st.markdown("**查询到的部署计划:**")
                plan_df = parse_deployment_plan_table(deployment_plan["deployment_plan"])
                st.dataframe(plan_df, use_container_width=True)
        return
    
    with st.container(border=True):
        st.subheader("🔍 发布计划查询")
        
        col1, col2 = st.columns(2)
        with col1:
            version = st.text_input(
                "**版本号**",
                placeholder="例如: 25R1.2",
                help="输入要发布的版本号"
            )
        with col2:
            environment = st.selectbox(
                "**环境**",
                ENVIRONMENTS,
                help="选择目标环境",
                key="single_workflow_environment"
            )
        
        # 自然语言查询
        st.markdown("**或使用自然语言查询:**")
        user_query = st.text_area(
            "描述您的发布需求",
            placeholder="例如: 列出 25R1.2 Prod 的发布计划",
            height=100
        )
        
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("🔍 查询发布计划", type="primary", use_container_width=True):
                query_release_plan(version, environment, user_query, force_refresh=False)
        
        with col2:
            if st.button("🔃 强制刷新", use_container_width=True, help="强制从数据库重新获取最新数据"):
                query_release_plan(version, environment, user_query, force_refresh=True)
        
        with col3:
            if st.button("🧪 使用测试数据", use_container_width=True):
                load_test_data()

def query_release_plan(version: str, environment: str, user_query: str, force_refresh: bool = False):
    """查询发布计划"""
    logger = logging.getLogger(__name__)
    logger.info(f"🔍 开始查询发布计划 - 版本: {version}, 环境: {environment}, 强制刷新: {force_refresh}")
    logger.info(f"📝 用户查询: {user_query}")

    with st.status("正在查询发布计划...", expanded=True) as status:
        try:
            if 'agent' not in st.session_state or st.session_state.agent is None:
                logger.error("❌ Agent未初始化")
                status.update(label="Agent未初始化", state="error")
                st.error("Agent未初始化，请检查配置")

                # 尝试重新初始化Agent
                try:
                    logger.info("🔄 尝试重新初始化Agent...")
                    from src.release_agent import ReleaseAgent
                    status.write("尝试重新初始化Agent...")
                    st.session_state.agent = ReleaseAgent()
                    status.write("Agent初始化成功")
                    logger.info("✅ Agent重新初始化成功")
                except Exception as init_e:
                    logger.error(f"❌ Agent重新初始化失败: {str(init_e)}")
                    st.error(f"Agent初始化失败: {str(init_e)}")
                    return
            
            # 构建查询
            if user_query.strip():
                query_text = user_query
            elif version and environment:
                query_text = f"列出 {version} {environment} 的发布计划"
            else:
                st.error("请输入版本号和环境，或使用自然语言描述")
                return
            
            logger.info("🔄 开始解析查询参数...")
            status.write("解析查询参数...")
            if force_refresh:
                status.write("强制刷新数据库连接...")
                logger.info("🔃 执行强制刷新查询")
            result = st.session_state.agent.process_user_request(query_text, force_refresh=force_refresh)

            if result["success"]:
                logger.info("✅ 查询成功")
                logger.info(f"📊 获取到部署计划数据")
                status.write("生成发布计划...")
                st.session_state.deployment_plan = result["data"]
                workflow_manager.update_workflow_step("plan_approval")
                status.update(label="查询完成！", state="complete")
                st.rerun()
            else:
                logger.error(f"❌ 查询失败: {result['message']}")
                status.update(label="查询失败", state="error")
                st.error(f"查询失败: {result['message']}")

        except Exception as e:
            logger.error(f"❌ 查询异常: {str(e)}")
            status.update(label="查询异常", state="error")
            st.error(f"查询异常: {str(e)}")

def load_test_data():
    """加载测试数据"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 开始加载测试数据...")

    st.session_state.deployment_plan = {
        "success": True,
        "version": "25R1.2",
        "environment": "Prod",
        "workflow_state": "plan_generated",
        "deployment_plan": [
            {
                "customer_name": "Customer A",
                "service_name": "Service 1",
                "deployment_date": "2024-12-15",
                "time_window": "10:00-12:00",
                "dependencies": "None"
            },
            {
                "customer_name": "Customer B", 
                "service_name": "Service 2",
                "deployment_date": "2024-12-15",
                "time_window": "14:00-16:00",
                "dependencies": "Service 1"
            }
        ],
        "jenkins_jobs": [
            {
                "job_name": "deploy-customer-a-service-1",
                "customer_name": "Customer A",
                "service_name": "Service 1",
                "deployment_date": "2024-12-15",
                "time_window": "10:00-12:00",
                "parameters": {
                    "VERSION": "25R1.2",
                    "ENVIRONMENT": "Prod",
                    "CUSTOMER": "Customer A"
                }
            },
            {
                "job_name": "deploy-customer-b-service-2",
                "customer_name": "Customer B",
                "service_name": "Service 2", 
                "deployment_date": "2024-12-15",
                "time_window": "14:00-16:00",
                "parameters": {
                    "VERSION": "25R1.2",
                    "ENVIRONMENT": "Prod",
                    "CUSTOMER": "Customer B"
                }
            }
        ]
    }

    logger.info("✅ 测试数据加载完成")
    logger.info(f"📊 加载了 {len(st.session_state.deployment_plan['deployment_plan'])} 个部署计划")
    logger.info(f"🔧 加载了 {len(st.session_state.deployment_plan['jenkins_jobs'])} 个Jenkins Jobs")

    workflow_manager.update_workflow_step("plan_approval")
    st.success("测试数据已加载")
    st.rerun()

def step_2_plan_approval():
    """步骤2: 计划审批"""
    if 'deployment_plan' not in st.session_state:
        return
    
    current_step = workflow_manager.get_current_step()
    is_current = current_step == "plan_approval"
    
    display_step_header(2, "计划审批", "👀", "审核和确认发布计划", is_current)
    
    deployment_plan = st.session_state.deployment_plan
    
    # 如果不是当前步骤且已完成，使用折叠展示
    if not is_current and deployment_plan.get("workflow_state") == "plan_approved":
        with st.expander("✅ 计划审批已完成 - 点击查看详情", expanded=False):
            # 显示基本信息
            col1, col2, col3 = st.columns(3)
            col1.metric("版本", deployment_plan.get("version", "N/A"))
            col2.metric("环境", deployment_plan.get("environment", "N/A"))
            # 修复计划数量计算：应该解析表格后计算列表长度
            plan_items = parse_deployment_plan_table(deployment_plan.get("deployment_plan", "")) if isinstance(deployment_plan.get("deployment_plan"), str) else deployment_plan.get("deployment_plan", [])
            plan_count = len(plan_items) if isinstance(plan_items, list) else 0
            col3.metric("计划数量", plan_count)
            
            # 显示部署计划表格
            if deployment_plan.get("deployment_plan"):
                st.markdown("**已审批的部署计划:**")
                plan_df = parse_deployment_plan_table(deployment_plan["deployment_plan"])
                st.dataframe(plan_df, use_container_width=True)
                
            # 显示Jenkins Jobs
            jenkins_jobs = deployment_plan.get("raw_jenkins_jobs", deployment_plan.get("jenkins_jobs", []))
            if jenkins_jobs and isinstance(jenkins_jobs, list):
                st.markdown("**已确认的Jenkins Jobs:**")
                jobs_data = []
                for job in jenkins_jobs:
                    if isinstance(job, dict):
                        jobs_data.append({
                            "Job名称": job.get("job_name", "N/A"),
                            "客户": job.get("customer_name", "N/A"),
                            "服务": job.get("service_name", "N/A"),
                            "部署时间": f"{job.get('deployment_date', 'N/A')} {job.get('time_window', 'N/A')}"
                        })
                if jobs_data:
                    st.dataframe(jobs_data, use_container_width=True)
        return
    
    with st.container(border=True):
        st.subheader("📋 发布计划详情")
        
        # 显示基本信息
        col1, col2, col3 = st.columns(3)
        col1.metric("版本", deployment_plan.get("version", "N/A"))
        col2.metric("环境", deployment_plan.get("environment", "N/A"))
        # 修复计划数量计算：应该解析表格后计算列表长度
        plan_items = parse_deployment_plan_table(deployment_plan.get("deployment_plan", "")) if isinstance(deployment_plan.get("deployment_plan"), str) else deployment_plan.get("deployment_plan", [])
        plan_count = len(plan_items) if isinstance(plan_items, list) else 0
        col3.metric("计划数量", plan_count)
        
        # 显示部署计划表格
        if deployment_plan.get("deployment_plan"):
            st.markdown("**部署计划:**")
            plan_df = parse_deployment_plan_table(deployment_plan["deployment_plan"])
            st.dataframe(plan_df, use_container_width=True)
        
        # 显示Jenkins Jobs
        jenkins_jobs = deployment_plan.get("raw_jenkins_jobs", deployment_plan.get("jenkins_jobs", []))
        if jenkins_jobs and isinstance(jenkins_jobs, list):
            st.markdown("**Jenkins Jobs:**")
            jobs_data = []
            for job in jenkins_jobs:
                # 安全检查job是否为字典
                if isinstance(job, dict):
                    jobs_data.append({
                        "Job名称": job.get("job_name", "N/A"),
                        "客户": job.get("customer_name", "N/A"),
                        "服务": job.get("service_name", "N/A"),
                        "部署时间": f"{job.get('deployment_date', 'N/A')} {job.get('time_window', 'N/A')}"
                    })
                else:
                    # 如果job不是字典，尝试转换为字符串显示
                    jobs_data.append({
                        "Job名称": str(job),
                        "客户": "N/A",
                        "服务": "N/A",
                        "部署时间": "N/A"
                    })

            if jobs_data:
                st.dataframe(jobs_data, use_container_width=True)
            else:
                st.info("没有找到有效的Jenkins Jobs数据")
        
        # 审批操作
        st.markdown("---")
        col1, col2 = st.columns([1, 1])
        
        with col1:
            if st.button("👍 确认计划正确", type="primary", use_container_width=True):
                approve_plan()
        
        with col2:
            if st.button("🔄 重新查询", use_container_width=True):
                reject_plan()

def approve_plan():
    """审批通过计划"""
    st.session_state.deployment_plan["workflow_state"] = "plan_approved"
    workflow_manager.update_workflow_step("execution")
    st.success("✅ 发布计划已确认，进入执行阶段")
    st.rerun()

def reject_plan():
    """拒绝计划，重新查询"""
    if 'deployment_plan' in st.session_state:
        del st.session_state.deployment_plan
    workflow_manager.update_workflow_step("plan_query")
    st.warning("已重置，请重新查询发布计划")
    st.rerun()

def step_3_execution_management():
    """步骤3: 执行管理"""
    if 'deployment_plan' not in st.session_state or st.session_state.deployment_plan.get("workflow_state") != "plan_approved":
        return
    
    current_step = workflow_manager.get_current_step()
    is_current = current_step == "execution"
    
    display_step_header(3, "执行管理", "🚀", "选择和执行Jenkins Jobs", is_current)
    
    # 如果不是当前步骤且已有执行状态，显示已选择的Jobs
    if not is_current and 'selected_jobs_for_execution' in st.session_state:
        with st.expander("✅ 执行管理已完成 - 点击查看已选择的Jobs", expanded=False):
            selected_jobs = st.session_state.get('selected_jobs_for_execution', [])
            if selected_jobs:
                st.markdown(f"**已选择执行 {len(selected_jobs)} 个Jobs:**")
                jobs_data = []
                for i, job in enumerate(selected_jobs):
                    if isinstance(job, dict):
                        jobs_data.append({
                            "序号": i + 1,
                            "Job名称": job.get("job_name", "N/A"),
                            "客户": job.get("customer_name", "N/A"),
                            "服务": job.get("service_name", "N/A"),
                            "部署时间": f"{job.get('deployment_date', 'N/A')} {job.get('time_window', 'N/A')}"
                        })
                if jobs_data:
                    st.dataframe(jobs_data, use_container_width=True)
            else:
                st.info("没有选择任何Jobs")
        return
    
    deployment_plan = st.session_state.deployment_plan
    jenkins_jobs = deployment_plan.get("raw_jenkins_jobs", deployment_plan.get("jenkins_jobs", []))
    
    if not jenkins_jobs:
        st.error("没有可执行的Jenkins Jobs")
        return
    
    with st.container(border=True):
        st.subheader("🎯 Jenkins Jobs 选择")
        
        # 时间窗口分析
        current_time = datetime.now()
        time_analysis = analyze_job_time_windows(jenkins_jobs, current_time)
        display_time_analysis_summary_enhanced(time_analysis, current_time)
        
        # Jobs选择 - 改为表格样式
        st.markdown("**选择要执行的Jobs:**")
        selected_jobs = []

        # 定义各个分类及其显示设置
        job_categories = [
            {
                "title": "🟢 可部署 Jobs",
                "jobs": time_analysis["deployable_jobs"],
                "expanded": True,
                "auto_select": True,  # 自动选中可部署的jobs
                "color": "#d4edda",
                "description": "当前时间窗口内可以执行的Jobs"
            },
            {
                "title": "🟡 未到时间 Jobs", 
                "jobs": time_analysis["future_jobs"],
                "expanded": False,
                "auto_select": False,
                "color": "#fff3cd",
                "description": "计划在未来时间执行的Jobs"
            },
            {
                "title": "🔴 已过期 Jobs",
                "jobs": time_analysis["expired_jobs"],
                "expanded": False,
                "auto_select": False,
                "color": "#f8d7da",
                "description": "已过期的Jobs，可能需要重新安排时间"
            },
            {
                "title": "⚪ 无时间限制 Jobs",
                "jobs": time_analysis["no_time_jobs"],
                "expanded": False,
                "auto_select": False,
                "color": "#f8f9fa",
                "description": "没有明确时间限制的Jobs"
            }
        ]

        for category in job_categories:
            if not category["jobs"]:
                continue
                
            with st.expander(f"**{category['title']} ({len(category['jobs'])})**", expanded=category["expanded"]):
                st.markdown(f"*{category['description']}*")
                
                # 创建表格数据
                table_data = []
                for job_info in category["jobs"]:
                    job = job_info["job"]
                    job_index = job_info["index"]
                    time_status = job_info["time_status"]
                    
                    # 时间状态信息
                    status_info = ""
                    if time_status.get("time_remaining"):
                        status_info = f"剩余: {time_status['time_remaining']}"
                    elif time_status.get("time_until_start"):
                        status_info = f"将于: {time_status['time_until_start']} 后开始"
                    elif time_status.get("time_since_end"):
                        status_info = f"已结束: {time_status['time_since_end']}"
                    else:
                        status_info = "无时间限制"
                    
                    table_data.append({
                        "选择": job_index,  # 用于checkbox的key
                        "Job名称": job.get("job_name", "N/A"),
                        "客户": job.get("customer_name", "N/A"),
                        "服务": job.get("service_name", "N/A"),
                        "部署日期": job.get("deployment_date", "N/A"),
                        "时间窗口": job.get("time_window", "N/A"),
                        "时间状态": status_info
                    })
                
                if table_data:
                    # 显示表格头
                    col_select, col_job, col_customer, col_service, col_date, col_time, col_status = st.columns([1, 3, 2, 2, 2, 2, 2])
                    
                    with col_select:
                        st.markdown("**选择**")
                    with col_job:
                        st.markdown("**Job名称**")
                    with col_customer:
                        st.markdown("**客户**")
                    with col_service:
                        st.markdown("**服务**")
                    with col_date:
                        st.markdown("**部署日期**")
                    with col_time:
                        st.markdown("**时间窗口**")
                    with col_status:
                        st.markdown("**时间状态**")
                    
                    st.markdown("---")
                    
                    # 显示表格数据
                    for row in table_data:
                        col_select, col_job, col_customer, col_service, col_date, col_time, col_status = st.columns([1, 3, 2, 2, 2, 2, 2])
                        
                        with col_select:
                            # 可部署的jobs默认选中
                            default_checked = category["auto_select"]
                            checkbox_label = f"选择Job {row['Job名称']}"
                            if st.checkbox(checkbox_label, value=default_checked, key=f"job_{row['选择']}", label_visibility="collapsed"):
                                selected_jobs.append(row['选择'])
                        
                        with col_job:
                            st.markdown(f"**{row['Job名称']}**")
                        with col_customer:
                            st.text(row['客户'])
                        with col_service:
                            st.text(row['服务'])
                        with col_date:
                            st.text(row['部署日期'])
                        with col_time:
                            st.text(row['时间窗口'])
                        with col_status:
                            # 根据时间状态设置不同颜色
                            if "剩余" in row['时间状态']:
                                st.success(row['时间状态'])
                            elif "将于" in row['时间状态']:
                                st.info(row['时间状态'])
                            elif "已结束" in row['时间状态']:
                                st.error(row['时间状态'])
                            else:
                                st.text(row['时间状态'])
                        
                        st.markdown("")  # 添加一点间距
        
        if selected_jobs:
            st.markdown("---")
            col1, col2 = st.columns(2)
            with col1:
                st.success(f"✅ 已选择 {len(selected_jobs)} 个Jobs")
            with col2:
                if st.button("🚀 确认执行选中的Jobs", type="primary", use_container_width=True):
                    start_execution(selected_jobs)
        else:
            st.warning("请至少选择一个Job来执行")

def start_execution(selected_indices: List[int]):
    """开始执行选中的Jobs"""
    logger = logging.getLogger(__name__)
    logger.info(f"🚀 开始执行选中的Jobs - 选中索引: {selected_indices}")

    deployment_plan = st.session_state.deployment_plan
    jenkins_jobs = deployment_plan.get("raw_jenkins_jobs", deployment_plan.get("jenkins_jobs", []))

    selected_jobs = [jenkins_jobs[i] for i in selected_indices]

    logger.info(f"📋 选中了 {len(selected_jobs)} 个Jobs准备执行")
    for i, job in enumerate(selected_jobs):
        if isinstance(job, dict):
            job_name = job.get('job_name', f'job_{i}')
            logger.info(f"  - Job {i+1}: {job_name}")

    # 显示执行确认
    st.session_state.selected_jobs_for_execution = selected_jobs
    st.session_state.show_execution_confirmation = True
    st.rerun()

def step_4_execution_confirmation():
    """步骤4: 执行确认"""
    logger = logging.getLogger(__name__)

    # 检查当前工作流状态
    current_step = workflow_manager.get_current_step()
    show_confirmation = st.session_state.get('show_execution_confirmation', False)
    has_execution_status = 'execution_status' in st.session_state

    logger.info(f"🔍 步骤4检查 - 显示确认: {show_confirmation}, 当前步骤: {current_step}")

    # 如果正在显示确认界面，显示完整的步骤4
    if show_confirmation:
        logger.info("🔍 进入执行确认步骤")
        display_step_header(4, "执行确认", "🔍", "确认Jenkins Job参数和执行顺序", True)
        
        selected_jobs = st.session_state.get('selected_jobs_for_execution', [])

        if not selected_jobs:
            st.error("没有选中的Jobs")
            return

        # 显示执行模式
        execution_mode = st.session_state.get('jenkins_execution_mode', 'simulation')
        
        if execution_mode == "real":
            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px 0;
                text-align: center;
                border: 2px solid #dc3545;
            ">
                <h4 style="margin: 0; color: white;">⚡ 真实执行模式</h4>
                <p style="margin: 5px 0 0 0;">
                    ⚠️ 将会触发真实的Jenkins Jobs，可能影响生产环境！
                </p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px 0;
                text-align: center;
                border: 2px solid #28a745;
            ">
                <h4 style="margin: 0; color: white;">🎭 模拟执行模式</h4>
                <p style="margin: 5px 0 0 0;">
                    ✅ 安全的模拟执行，不会产生实际部署效果
                </p>
            </div>
            """, unsafe_allow_html=True)

        with st.container(border=True):
            st.subheader("📋 执行确认")
            
            # 合并显示执行顺序规划和Jobs参数
            st.markdown("---")
            display_combined_execution_info(selected_jobs)
            st.markdown("---")
            
            st.markdown("---")
            
            # 重要提醒信息
            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px 0;
                text-align: center;
            ">
                <h4 style="margin: 0; color: white;">⚠️ 重要确认步骤</h4>
                <p style="margin: 5px 0 0 0;">
                    即将执行 {len(selected_jobs)} 个Jenkins Jobs，请仔细检查所有参数和执行模式！
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            # 显示切换执行模式的提示
            execution_mode = st.session_state.get('jenkins_execution_mode', 'simulation')
            if execution_mode == "real":
                st.info("💡 如需切换到模拟模式，请在左侧边栏Jenkins配置中修改执行模式")
            else:
                st.info("💡 如需切换到真实执行模式，请在左侧边栏Jenkins配置中修改执行模式")
            
            col1, col2 = st.columns([1, 1])

            with col1:
                button_text = "⚡ 确认真实执行" if execution_mode == "real" else "🎭 确认模拟执行"
                if st.button(button_text, type="primary", use_container_width=True):
                    logger.info("✅ 用户确认执行Jobs")
                    execute_jobs()

            with col2:
                if st.button("❌ 取消执行", use_container_width=True):
                    logger.info("❌ 用户取消执行Jobs")
                    cancel_execution()
        return

    # 如果当前步骤是monitoring或completed，且有执行状态，显示已完成的步骤4
    current_has_execution_status = 'execution_status' in st.session_state and st.session_state.execution_status
    logger.info(f"🔍 步骤4显示检查: 步骤={current_step}, 有执行状态={current_has_execution_status}")
    if hasattr(st.session_state, 'execution_status'):
        logger.info(f"📊 execution_status长度: {len(st.session_state.execution_status) if st.session_state.execution_status else 0}")
    else:
        logger.info("📊 execution_status不存在")
    
    if current_step in ["monitoring", "completed"] and current_has_execution_status:
        logger.info("✅ 显示步骤4已完成状态")
        display_step_header(4, "执行确认", "🔍", "确认Jenkins Job参数和执行顺序", False)
        
        # 使用折叠方式显示已确认的Jobs详情
        selected_jobs = st.session_state.get('selected_jobs_for_execution', [])
        execution_status = st.session_state.execution_status
        job_count = len(execution_status)
        
        with st.expander(f"✅ 执行确认已完成 - 点击查看已确认的 {job_count} 个Jobs详情", expanded=False):
            if selected_jobs:
                display_combined_execution_info_with_status(selected_jobs, execution_status)
            else:
                st.info("没有找到已确认的Jobs信息")
        return

    # 其他情况下不显示步骤4
    logger.info("⏭️ 跳过步骤4显示 - 不满足显示条件")

def execute_jobs():
    """执行Jobs"""
    import time
    from datetime import datetime
    logger = logging.getLogger(__name__)
    selected_jobs = st.session_state.get('selected_jobs_for_execution', [])
    
    # 获取执行模式
    execution_mode = st.session_state.get('jenkins_execution_mode', 'simulation')

    logger.info(f"⚡ 开始执行 {len(selected_jobs)} 个Jenkins Jobs - 模式: {execution_mode}")

    try:
        # 显示启动状态（在状态更新之前先给用户反馈）
        with st.status("正在启动Jenkins Jobs...", expanded=True) as status:
            status.write(f"执行模式: {'🎭 模拟执行' if execution_mode == 'simulation' else '⚡ 真实执行'}")
            status.write("正在初始化智能执行环境...")
            
            # 检查是否可以使用智能执行管理器
            agent = st.session_state.get('agent')
            if agent and hasattr(agent, 'workflow') and hasattr(agent.workflow, 'execution_manager'):
                execution_manager = agent.workflow.execution_manager
                
                # 在模拟模式下，临时移除jenkins_client以确保纯模拟执行
                if execution_mode == "simulation":
                    original_jenkins_client = execution_manager.jenkins_client
                    execution_manager.jenkins_client = None
                    status.write("🎭 模拟模式：禁用Jenkins客户端，确保纯模拟执行")
                else:
                    original_jenkins_client = None
                
                # 使用智能执行管理器
                status.write("🧠 启用智能执行管理器...")
                status.write("📊 按客户分组，同一客户内按部署顺序串行执行...")
                
                # 启动智能执行管理
                result = execution_manager.start_execution(selected_jobs)
                
                if result["success"]:
                    status.write(f"✅ 智能执行管理器启动成功: {result['groups']}个执行组")
                    
                    # 更新agent状态中的执行管理器
                    if agent.current_state:
                        agent.current_state.execution_manager = execution_manager
                    
                    # 设置为智能执行模式
                    smart_execution_active = True
                    status.write("🎯 智能执行管理器已接管，开始触发第一批Jobs...")
                    
                    # 立即触发第一批可以执行的Jobs
                    ready_jobs = execution_manager.get_next_jobs_to_execute()
                    status.write(f"📋 发现 {len(ready_jobs)} 个Job可立即执行")
                    
                    for group_id, job in ready_jobs:
                        job_name = job.get("job_name", "unknown")
                        status.write(f"🚀 触发执行组 {group_id} 的Job: {job_name}")
                        
                        trigger_result = execution_manager.trigger_job(group_id, job)
                        if trigger_result["success"]:
                            if trigger_result.get("simulation"):
                                status.write(f"   🎭 模拟执行启动")
                                # 在模拟模式下，不立即标记为完成，让它保持RUNNING状态
                                # 通过自动刷新机制逐步模拟执行过程
                                status.write(f"   🔄 模拟执行进行中，将通过刷新机制逐步显示进度")
                            else:
                                status.write(f"   ✅ Build号: {trigger_result.get('build_number')}")
                        else:
                            status.write(f"   ❌ 触发失败: {trigger_result['message']}")
                    
                    # 在模拟模式下，只触发第一批Jobs，让后续的通过刷新机制逐步模拟
                    if execution_mode == "simulation":
                        status.write("🎭 模拟模式：启动智能执行，Jobs将逐步执行...")
                        # 只触发第一批Jobs，不立即完成，让refresh_execution_status来处理逐步模拟
                        # 这样Jobs会保持RUNNING状态，通过自动刷新逐步更新console输出和状态
                    
                    # 恢复原始的jenkins_client
                    if original_jenkins_client is not None:
                        execution_manager.jenkins_client = original_jenkins_client
                        status.write("🔧 恢复Jenkins客户端配置")
                    
                    status.write("🎯 智能执行管理器正在运行中...")
                else:
                    status.write(f"⚠️ 智能执行管理器启动失败: {result['message']}")
                    status.write("🔄 回退到传统执行方式...")
                    smart_execution_active = False
            else:
                status.write("⚠️ 智能执行管理器不可用，使用传统执行方式...")
                smart_execution_active = False
            
            # 初始化状态变量
            execution_status = {}
            smart_execution_status = {}
            
            # 如果使用智能执行管理器，跳过传统的初始化
            if not smart_execution_active:
                # 传统执行方式：初始化执行状态
                status.write("正在初始化传统执行环境...")

            for job in selected_jobs:
                # 安全检查job是否为字典
                if isinstance(job, dict):
                    job_name = job.get('job_name', f'unknown_job_{len(execution_status)}')
                else:
                    job_name = f'job_{len(execution_status)}'

                logger.info(f"🔧 初始化Job执行状态: {job_name}")
                execution_status[job_name] = {
                    "status": "PENDING",
                    "build_number": None,
                    "start_time": datetime.now(),
                    "logs": [],
                    "job_data": job,  # 保存原始job数据供后续使用
                    "execution_mode": execution_mode  # 记录执行模式
                }
            
            status.write(f"已初始化 {len(execution_status)} 个Jobs")
            
            # 只有在非智能执行模式下才进行传统的Job触发
            if not smart_execution_active:
                # 根据执行模式处理Jobs
                if execution_mode == "real":
                    status.write("🔗 连接Jenkins服务器...")
                    success_count = 0
                    
                    # 检查Jenkins客户端是否可用
                    if 'agent' not in st.session_state or not st.session_state.agent:
                        status.update(label="❌ Agent未初始化，无法连接Jenkins", state="error")
                        st.error("Agent未初始化，无法连接Jenkins。请检查配置或切换到模拟模式。")
                        return
                    
                    jenkins_client = st.session_state.agent.jenkins_client
                    if not jenkins_client:
                        status.update(label="❌ Jenkins客户端不可用", state="error")
                        st.error("Jenkins客户端不可用。请检查Jenkins配置或切换到模拟模式。")
                        return
                    
                    # 逐个触发Jenkins Jobs
                    for job_name, status_info in execution_status.items():
                        job_data = status_info["job_data"]
                        
                        if isinstance(job_data, dict):
                            parameters = job_data.get('parameters', {})
                            status.write(f"🚀 触发Job: {job_name}")
                            
                            try:
                                # 调用真实的Jenkins API
                                result = jenkins_client.trigger_job(job_name, parameters)
                                
                                if result["success"]:
                                    status_info["status"] = "RUNNING"
                                    status_info["build_number"] = result.get("build_number")
                                    status_info["queue_item_number"] = result.get("queue_item_number")
                                    status_info["logs"] = [
                                        f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Job已成功触发",
                                        f"[{datetime.now().strftime('%H:%M:%S')}] Queue Item: {result.get('queue_item_number')}",
                                        f"[{datetime.now().strftime('%H:%M:%S')}] Build Number: {result.get('build_number', 'Pending...')}"
                                    ]
                                    success_count += 1
                                    logger.info(f"✅ Job {job_name} 触发成功")
                                else:
                                    status_info["status"] = "FAILURE"
                                    status_info["logs"] = [
                                        f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Job触发失败",
                                        f"[{datetime.now().strftime('%H:%M:%S')}] 错误信息: {result.get('message', 'Unknown error')}"
                                    ]
                                    logger.error(f"❌ Job {job_name} 触发失败: {result.get('message')}")
                                    
                            except Exception as e:
                                status_info["status"] = "FAILURE"
                                status_info["logs"] = [
                                    f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Job触发异常",
                                    f"[{datetime.now().strftime('%H:%M:%S')}] 异常信息: {str(e)}"
                                ]
                                logger.error(f"❌ Job {job_name} 触发异常: {str(e)}")
                    
                    if success_count > 0:
                        status.write(f"✅ 成功触发 {success_count}/{len(execution_status)} 个Jobs")
                    else:
                        status.update(label="❌ 所有Jobs触发失败", state="error")
                        st.error("所有Jobs触发失败，请检查Jenkins配置和网络连接。")
                        return
                
                else:
                    # 模拟模式
                    status.write("🎭 使用模拟执行模式")
                    for job_name, status_info in execution_status.items():
                        status_info["logs"] = [
                            f"[{datetime.now().strftime('%H:%M:%S')}] 🎭 模拟模式已启动",
                            f"[{datetime.now().strftime('%H:%M:%S')}] Job: {job_name}",
                            f"[{datetime.now().strftime('%H:%M:%S')}] 注意: 这是模拟执行，不会产生实际效果"
                        ]

            status.write("更新工作流状态...")

            # 按正确顺序设置状态，确保状态同步
            logger.info("🔄 更新工作流状态...")

            # 1. 先关闭执行确认界面（避免步骤4继续显示）
            st.session_state.show_execution_confirmation = False
            logger.info("✅ 执行确认界面已关闭")

            # 2. 设置执行状态（在更新工作流步骤之前）
            if smart_execution_active:
                # 智能执行模式：直接从selected_jobs创建execution_status格式
                try:
                    status.write("📊 创建智能执行状态...")
                    smart_execution_status = {}
                    
                    # 直接基于selected_jobs创建状态，因为此时执行管理器刚启动
                    status.write(f"📋 处理 {len(selected_jobs)} 个selected_jobs...")
                    for i, job in enumerate(selected_jobs):
                        if isinstance(job, dict):
                            job_name = job.get('job_name', f'job_{i}')
                            customer_name = job.get('customer_name', 'Unknown')
                            status.write(f"   📝 创建Job状态: {job_name} ({customer_name})")
                            
                            # 为模拟模式创建更完整的初始状态
                            if execution_mode == "simulation":
                                import random
                                build_number = random.randint(100, 999)
                                
                                # 根据job在列表中的位置决定初始状态
                                # 第一个job开始RUNNING，其他的先PENDING
                                is_first_job = (i == 0)
                                initial_status = "RUNNING" if is_first_job else "PENDING"
                                
                                initial_logs = [
                                    f"[{datetime.now().strftime('%H:%M:%S')}] 🎭 模拟执行模式启动",
                                    f"[{datetime.now().strftime('%H:%M:%S')}] 🚀 开始执行Job: {job_name}",
                                    f"[{datetime.now().strftime('%H:%M:%S')}] 📋 客户: {customer_name}",
                                    f"[{datetime.now().strftime('%H:%M:%S')}] 🔢 模拟Build号: {build_number}"
                                ]
                                
                                if not is_first_job:
                                    initial_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] ⏳ 等待前置任务完成...")
                                    
                            else:
                                build_number = None
                                initial_logs = []
                                initial_status = "PENDING"
                            
                            smart_execution_status[job_name] = {
                                "status": initial_status,
                                "build_number": build_number,
                                "start_time": datetime.now(),
                                "customer_name": customer_name,
                                "execution_mode": execution_mode,
                                "is_smart_execution": True,
                                "logs": initial_logs,
                                "job_data": job  # 保存完整的job数据
                            }
                        else:
                            status.write(f"   ⚠️ Job {i} 不是字典格式: {type(job)}")
                    
                    st.session_state.execution_status = smart_execution_status
                    logger.info(f"✅ 智能执行模式已启动，状态由执行管理器管理: {len(smart_execution_status)} 个Jobs")
                except Exception as e:
                    logger.error(f"❌ 创建智能执行状态失败: {e}")
                    status.write(f"❌ 智能执行状态创建失败: {str(e)}")
                    status.write("🔄 创建基础状态...")
                    # 如果失败，创建基础状态
                    basic_status = {}
                    for i, job in enumerate(selected_jobs):
                        job_name = job.get('job_name', f'job_{i}') if isinstance(job, dict) else f'job_{i}'
                        basic_status[job_name] = {
                            "status": "PENDING",
                            "execution_mode": execution_mode,
                            "is_smart_execution": True
                        }
                        status.write(f"   📝 基础Job状态: {job_name}")
                    st.session_state.execution_status = basic_status
                    status.write(f"✅ 使用基础状态格式: {len(basic_status)} 个Jobs")
                    logger.info(f"✅ 使用基础状态格式: {len(basic_status)} 个Jobs")
            else:
                # 传统模式：设置执行状态
                st.session_state.execution_status = execution_status
                logger.info(f"✅ 设置执行状态: {len(execution_status)} 个Jobs")

            # 3. 最后更新工作流步骤到监控阶段
            workflow_manager.update_workflow_step("monitoring")
            logger.info("✅ 工作流步骤已更新到监控阶段")

            status.write("切换到监控阶段...")
            status.update(label="Jobs已启动，进入监控阶段", state="complete")

        if smart_execution_active:
            logger.info(f"✅ 智能执行管理器已启动，进入监控阶段")
            st.success("✅ 智能执行管理器已启动，Jobs将按客户分组和部署顺序执行...")
        else:
            logger.info(f"✅ {len(execution_status)} 个Jobs启动成功，进入监控阶段")
            if execution_mode == "real":
                st.success("✅ Jenkins Jobs已真实启动，正在切换到监控阶段...")
            else:
                st.success("✅ 模拟Jobs已启动，正在切换到监控阶段...")

        # 立即刷新页面以显示监控界面（移除不必要的延迟）
        st.rerun()

    except Exception as e:
        logger.error(f"❌ 启动Jobs失败: {str(e)}")
        st.error(f"启动Jobs失败: {str(e)}")
        if execution_mode == "real":
            st.error("建议切换到模拟模式进行测试")

def cancel_execution():
    """取消执行"""
    logger = logging.getLogger(__name__)
    logger.info("🚫 取消执行操作")

    st.session_state.show_execution_confirmation = False
    if 'selected_jobs_for_execution' in st.session_state:
        del st.session_state.selected_jobs_for_execution
    st.warning("已取消执行")
    st.rerun()

def step_5_monitoring():
    """步骤5: 状态监控"""
    logger = logging.getLogger(__name__)
    current_step = workflow_manager.get_current_step()
    has_execution_status = 'execution_status' in st.session_state

    logger.info(f"🔍 步骤5检查 - 当前步骤: {current_step}")
    logger.info(f"📊 执行状态存在: {has_execution_status}")

    # 优化显示条件逻辑
    should_show_step5 = False
    
    # 条件1：当前步骤就是monitoring或completed
    if current_step in ["monitoring", "completed"]:
        should_show_step5 = True
        logger.info("✅ 显示条件1满足：当前步骤为monitoring/completed")
    
    # 条件2：有执行状态且不在早期步骤（避免在plan_query时误显示）
    elif has_execution_status and current_step not in ["plan_query", "plan_approval"]:
        should_show_step5 = True
        logger.info("✅ 显示条件2满足：有执行状态且步骤合适")
    
    # 条件3：有智能执行管理器且真正有执行任务（更严格的检查）
    elif current_step not in ["plan_query", "plan_approval", "execution"]:
        # 检查是否有真正的智能执行任务
        has_real_smart_execution = False
        try:
            if ('agent' in st.session_state and 
                hasattr(st.session_state.agent, 'workflow') and 
                hasattr(st.session_state.agent.workflow, 'execution_manager') and 
                st.session_state.agent.workflow.execution_manager):
                
                execution_manager = st.session_state.agent.workflow.execution_manager
                execution_summary = execution_manager.get_execution_summary()
                
                # 检查是否有真正的执行任务（不是空的管理器）
                total_jobs = execution_summary.get('total_jobs', 0)
                if total_jobs > 0:
                    has_real_smart_execution = True
                    logger.info(f"🧠 检测到智能执行管理器有 {total_jobs} 个任务")
        except Exception as e:
            logger.warning(f"检查智能执行状态失败: {e}")
        
        if has_real_smart_execution:
            should_show_step5 = True
            logger.info("✅ 显示条件3满足：有真实的智能执行任务")
        else:
            logger.info("❌ 显示条件3不满足：智能执行管理器为空或无任务")

    if not should_show_step5:
        logger.info("⏭️ 跳过步骤5显示 - 不满足显示条件")
        return

    # 判断是否为当前活跃步骤
    is_current = current_step == "monitoring"
    is_completed = current_step == "completed"

    logger.info(f"✅ 显示步骤5 - 状态监控 (当前: {is_current}, 已完成: {is_completed})")
    display_step_header(5, "状态监控", "📊", "监控执行状态和Console输出", is_current)

    # 如果是monitoring步骤但没有任何执行状态，显示等待信息
    if current_step == "monitoring" and not has_execution_status:
        # 检查是否有智能执行管理器状态
        has_smart_execution = False
        try:
            if ('agent' in st.session_state and 
                hasattr(st.session_state.agent, 'workflow') and 
                hasattr(st.session_state.agent.workflow, 'execution_manager') and 
                st.session_state.agent.workflow.execution_manager):
                
                execution_manager = st.session_state.agent.workflow.execution_manager
                execution_summary = execution_manager.get_execution_summary()
                if execution_summary.get('total_jobs', 0) > 0:
                    has_smart_execution = True
                    logger.info("🧠 检测到智能执行管理器状态")
        except Exception as e:
            logger.warning(f"检查智能执行状态失败: {e}")

        if not has_smart_execution:
            logger.info("⏳ 当前步骤是监控但无执行状态，显示等待界面")
            with st.container(border=True):
                st.subheader("🔄 实时监控")
                st.info("⏳ 正在初始化监控状态，Jobs即将开始执行...")

                # 显示基本的监控界面框架
                col1, col2, col3 = st.columns([1, 1, 2])
                with col1:
                    st.checkbox("自动刷新", value=True, disabled=True)
                with col2:
                    st.selectbox("刷新间隔", [5, 10, 15, 30], index=0, disabled=True)
                with col3:
                    st.button("🔄 手动刷新", use_container_width=True, disabled=True)

                # 显示等待状态
                st.markdown("---")
                st.info("等待Jobs执行状态初始化...")
            return

    # 优先尝试显示智能执行管理器的状态
    smart_execution_displayed = False
    if 'agent' in st.session_state:
        try:
            agent_result = st.session_state.agent.get_execution_status()
            if agent_result["success"] and agent_result["data"].get("is_smart_execution"):
                smart_state = agent_result["data"]
                group_status = smart_state.get("group_status", {})
                execution_summary = smart_state.get("execution_summary", {})
                
                if group_status or execution_summary.get('total_jobs', 0) > 0:
                    logger.info("🧠 使用智能执行管理器状态显示")
                    display_smart_execution_monitoring(group_status, execution_summary, is_completed)
                    smart_execution_displayed = True
        except Exception as e:
            logger.warning(f"获取智能执行状态失败: {e}")
    
    # 如果没有显示智能执行状态，且有传统execution_status，则显示传统状态
    if not smart_execution_displayed and has_execution_status:
        execution_status = st.session_state.execution_status
        logger.info(f"📋 显示传统执行状态，包含 {len(execution_status)} 个Jobs")
        display_traditional_execution_monitoring(execution_status, is_completed)
    
    # 如果两种状态都没有，显示空状态（仅在非monitoring步骤时）
    elif not smart_execution_displayed and not has_execution_status and current_step != "monitoring":
        logger.info("📋 无执行状态，显示空监控界面")
        with st.container(border=True):
            st.subheader("🔄 实时监控")
            st.info("暂无执行中的Jobs")

def display_traditional_execution_monitoring(execution_status: Dict, is_completed: bool):
    """显示传统执行状态监控"""
    logger = logging.getLogger(__name__)
    
    with st.container(border=True):
        # 根据完成状态调整标题
        if is_completed:
            st.subheader("📋 执行结果总览")
        else:
            st.subheader("🔄 实时监控")

        # 自动刷新控制（只在monitoring状态下启用）
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            auto_refresh = st.checkbox("自动刷新", value=not is_completed, disabled=is_completed)
        with col2:
            # 为模拟执行提供更短的刷新间隔选项
            has_simulation_jobs = any(
                status.get('execution_mode') == 'simulation' and status.get('status') == 'RUNNING'
                for status in execution_status.values()
            )
            if has_simulation_jobs:
                refresh_interval = st.selectbox("刷新间隔", [2, 3, 5, 10], index=0, disabled=is_completed, 
                                              help="模拟执行使用较短间隔以显示逐步进度")
            else:
                refresh_interval = st.selectbox("刷新间隔", [5, 10, 15, 30], index=0, disabled=is_completed)
        with col3:
            if st.button("🔄 手动刷新", use_container_width=True, disabled=is_completed):
                refresh_execution_status()

        # 总体状态
        total_jobs = len(execution_status)
        completed_jobs = sum(1 for status in execution_status.values()
                           if status.get('status') in ['SUCCESS', 'FAILURE', 'ABORTED'])

        col1, col2, col3, col4 = st.columns(4)
        col1.metric("总Jobs", total_jobs)
        col2.metric("已完成", completed_jobs)
        col3.metric("进度", f"{int(completed_jobs/total_jobs*100)}%" if total_jobs > 0 else "0%")

        # 状态统计
        success_count = sum(1 for status in execution_status.values() if status.get('status') == 'SUCCESS')
        running_count = sum(1 for status in execution_status.values() if status.get('status') == 'RUNNING')
        failed_count = sum(1 for status in execution_status.values()
                         if status.get('status') in ['FAILURE', 'ABORTED'])

        col4.metric("成功/运行/失败", f"{success_count}/{running_count}/{failed_count}")

        # 进度条
        if total_jobs > 0:
            progress = completed_jobs / total_jobs
            progress_text = f"整体进度: {completed_jobs}/{total_jobs}"
            if is_completed:
                progress_text += " (已完成)"
            st.progress(progress, text=progress_text)

        st.markdown("---")

        # 显示每个Job的状态
        for job_name, status_info in execution_status.items():
            display_job_monitoring(job_name, status_info)

        # 自动刷新逻辑 - 只在monitoring状态下且有活跃jobs时启用
        has_active_jobs = any(status.get('status') in ['PENDING', 'RUNNING'] for status in execution_status.values())
        
        if auto_refresh and has_active_jobs and not is_completed:
            # 使用session state来跟踪上次刷新时间
            current_time = time.time()
            last_refresh = st.session_state.get('last_refresh_time', 0)
            
            # 如果是第一次进入或者达到刷新间隔，立即刷新
            if last_refresh == 0 or current_time - last_refresh >= refresh_interval:
                st.session_state.last_refresh_time = current_time
                refresh_execution_status()
                st.rerun()
            else:
                # 显示下次刷新倒计时
                remaining_time = refresh_interval - (current_time - last_refresh)
                st.info(f"⏱️ 下次自动刷新: {int(remaining_time)}秒后")
                # 使用短暂等待后重新运行，而不是JavaScript
                time.sleep(1)  # 短暂等待
                st.rerun()
        elif auto_refresh and not has_active_jobs and not is_completed:
            # 所有Job都已完成，显示提示信息
            st.success("🎉 所有Job已完成，自动刷新已停止")
        elif is_completed:
            # 工作流已完成，显示最终状态
            if success_count == total_jobs:
                st.success("🎉 所有Jobs执行成功！工作流已完成")
            elif failed_count > 0:
                st.error(f"⚠️ {failed_count}个Jobs执行失败，请检查详细日志")
            else:
                st.info("📋 工作流执行完成")

def display_job_monitoring(job_name: str, status_info: Dict[str, Any]):
    """显示单个Job的监控信息"""
    status = status_info.get('status', 'UNKNOWN')
    build_number = status_info.get('build_number')
    start_time = status_info.get('start_time')
    logs = status_info.get('logs', [])
    job_data = status_info.get('job_data', {})

    # 状态颜色
    status_color = get_status_color(status)

    with st.expander(f"{job_name} - {status}", expanded=status == 'RUNNING'):
        # 显示build参数（折叠显示）
        if job_data and job_data.get('parameters'):
            with st.expander("📋 Build参数", expanded=False):
                parameters = job_data.get('parameters', {})
                if isinstance(parameters, dict):
                    col1, col2 = st.columns(2)
                    param_items = list(parameters.items())
                    mid_point = len(param_items) // 2
                    
                    with col1:
                        for key, value in param_items[:mid_point]:
                            st.write(f"**{key}:** `{value}`")
                    
                    with col2:
                        for key, value in param_items[mid_point:]:
                            st.write(f"**{key}:** `{value}`")
                else:
                    st.write("参数格式错误")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.write(f"**状态:** :{status_color}[{status}]")
            if build_number:
                st.write(f"**Build号:** {build_number}")

        with col2:
            if start_time:
                st.write(f"**开始时间:** {start_time.strftime('%H:%M:%S')}")
            duration = ""
            if start_time:
                elapsed = datetime.now() - start_time
                duration = f"{int(elapsed.total_seconds())}秒"
                st.write(f"**运行时长:** {duration}")

        with col3:
            execution_mode = status_info.get('execution_mode', 'simulation')
            if status == 'RUNNING':
                if execution_mode == 'simulation':
                    st.write("**执行模式:** 🎭 模拟")
                if st.button(f"⏹️ 停止 {job_name}", key=f"stop_{job_name}"):
                    stop_job(job_name)

        # 为智能执行的模拟Jobs显示模拟进度
        if (status == 'RUNNING' and execution_mode == 'simulation' and 
            status_info.get('is_smart_execution')):
            st.markdown("---")
            st.markdown("**🎭 模拟执行进度:**")
            
            # 根据日志数量计算模拟进度
            log_count = len(logs)
            if log_count > 4:  # 跳过初始的4条日志
                effective_logs = log_count - 4
                max_stage_logs = 11  # simulate_console_output_for_smart_execution中的stage数量
                progress = min(effective_logs / max_stage_logs, 1.0)
                
                # 确定当前模拟阶段
                if effective_logs <= 3:
                    current_stage = "🔧 环境初始化"
                elif effective_logs <= 6:
                    current_stage = "🔨 构建和测试"
                elif effective_logs <= 9:
                    current_stage = "🚀 部署执行"
                elif effective_logs <= 11:
                    current_stage = "🏥 健康检查"
                else:
                    current_stage = "✅ 执行完成"
                
                st.progress(progress, text=f"当前阶段: {current_stage}")
                
                if progress < 1.0:
                    estimated_remaining = int((1 - progress) * 30)  # 估算剩余秒数
                    if estimated_remaining > 0:
                        st.caption(f"⏱️ 预计剩余时间: ~{estimated_remaining}秒")
            else:
                st.progress(0.1, text="🚀 正在启动...")

        # 显示当前Stage信息（如果是运行中的真实Job）
        elif status == 'RUNNING' and build_number and execution_mode == 'real':
            # 优先使用状态刷新时保存的stage信息
            current_stage = status_info.get('current_stage')
            stages = status_info.get('all_stages', [])
            
            # 如果没有保存的stage信息，实时获取
            if not current_stage and not stages:
                try:
                    if 'agent' in st.session_state and st.session_state.agent:
                        jenkins_client = st.session_state.agent.jenkins_client
                        if jenkins_client:
                            stages_result = jenkins_client.get_pipeline_stages(job_name, build_number)
                            if stages_result.get("success"):
                                current_stage = stages_result.get("current_stage")
                                stages = stages_result.get("stages", [])
                except Exception as e:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"获取stage信息失败: {e}")
            
            # 显示stage信息
            if current_stage:
                st.markdown("---")
                st.markdown("**🎯 当前Stage:**")
                
                # 显示当前stage
                stage_status = current_stage.get('status', 'UNKNOWN')
                stage_name = current_stage.get('name', '未知Stage')
                
                if stage_status == 'IN_PROGRESS':
                    st.markdown(f"🔄 **{stage_name}** - 执行中")
                elif stage_status == 'SUCCESS':
                    st.markdown(f"✅ **{stage_name}** - 已完成")
                elif stage_status == 'FAILED':
                    st.markdown(f"❌ **{stage_name}** - 失败")
                else:
                    st.markdown(f"🔍 **{stage_name}** - {stage_status}")
                
                # 显示stage进度
                if len(stages) > 1:
                    completed_stages = sum(1 for s in stages if s.get('status') in ['SUCCESS', 'FAILED', 'ABORTED'])
                    progress = completed_stages / len(stages)
                    st.progress(progress, text=f"Pipeline进度: {completed_stages}/{len(stages)} stages")
            
            elif stages:
                st.markdown("---")
                st.markdown("**🎯 Pipeline状态:**")
                st.markdown("🔄 Pipeline执行中...")
                
                # 显示总体进度
                completed_stages = sum(1 for s in stages if s.get('status') in ['SUCCESS', 'FAILED', 'ABORTED'])
                if len(stages) > 0:
                    progress = completed_stages / len(stages)
                    st.progress(progress, text=f"Pipeline进度: {completed_stages}/{len(stages)} stages")

        # 显示Console输出
        if logs:
            execution_mode = status_info.get('execution_mode', 'simulation')
            if execution_mode == 'simulation':
                st.markdown("**🎭 模拟Console输出 (最新20行):**")
                st.caption("💡 这是模拟执行的虚拟输出，仅供演示")
            else:
                st.markdown("**📡 Jenkins Console输出 (最新20行):**")
            
            log_text = "\n".join(logs[-20:])  # 只显示最新20行
            st.code(log_text, language="bash")
        else:
            execution_mode = status_info.get('execution_mode', 'simulation')
            if execution_mode == 'simulation':
                st.info("🎭 正在生成模拟Console输出...")
            else:
                st.info("📡 等待Jenkins Console输出...")

def refresh_execution_status():
    """刷新执行状态"""
    logger = logging.getLogger(__name__)

    # 优先尝试刷新智能执行管理器状态
    smart_execution_refreshed = False
    if 'agent' in st.session_state:
        try:
            agent_result = st.session_state.agent.get_execution_status()
            if agent_result["success"] and agent_result["data"].get("is_smart_execution"):
                smart_state = agent_result["data"]
                group_status = smart_state.get("group_status", {})
                execution_summary = smart_state.get("execution_summary", {})
                
                if group_status or execution_summary.get('total_jobs', 0) > 0:
                    logger.info("🧠 成功刷新智能执行管理器状态")
                    
                    # 同步智能执行管理器的状态到UI的execution_status
                    if 'execution_status' in st.session_state:
                        ui_execution_status = st.session_state.execution_status
                        execution_manager = st.session_state.agent.workflow.execution_manager
                        
                        # 只同步智能执行的Jobs，避免影响其他类型的Jobs
                        for job_name, job_status in execution_manager.job_status.items():
                            if job_name in ui_execution_status:
                                job_ui_info = ui_execution_status[job_name]
                                
                                # 检查是否是智能执行的Job（通过标记判断）
                                if job_ui_info.get('is_smart_execution', False):
                                    old_status = job_ui_info.get('status')
                                    new_status = job_status.value if hasattr(job_status, 'value') else str(job_status)
                                    
                                    # 更新状态
                                    job_ui_info['status'] = new_status
                                    
                                    # 获取智能执行管理器中的job结果信息
                                    job_result = execution_manager.job_results.get(job_name, {})
                                    if job_result.get('build_number'):
                                        job_ui_info['build_number'] = job_result['build_number']
                                    
                                    # 如果状态发生变化，添加状态变化日志
                                    if old_status != new_status:
                                        if 'logs' not in job_ui_info:
                                            job_ui_info['logs'] = []
                                        job_ui_info['logs'].append(
                                            f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 智能执行状态更新: {old_status} → {new_status}"
                                        )
                                        logger.info(f"🔄 同步智能执行Job {job_name} 状态: {old_status} → {new_status}")
                                    
                                    # ------------------------------
                                    # 下面这段原本错位的逻辑挪回循环内部，确保每个 Job 都能生成模拟输出
                                    # ------------------------------
                                    execution_mode = job_ui_info.get('execution_mode', 'simulation')
                                    if execution_mode == 'simulation':
                                        # 只为正在执行或等待的Jobs更新console输出，避免重复处理已完成的Jobs
                                        if new_status in ['RUNNING', 'PENDING']:
                                            logger.info(f"🔄 为Job {job_name} 更新console输出 (状态: {new_status})")
                                            simulate_console_output_for_smart_execution(job_name, job_ui_info, execution_manager)
                                        elif new_status == 'SUCCESS' and old_status != 'SUCCESS':
                                            # 只在首次变为SUCCESS时添加完成日志
                                            if 'logs' not in job_ui_info:
                                                job_ui_info['logs'] = []
                                            job_ui_info['logs'].append(
                                                f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 智能模拟执行完成！"
                                            )
                                            logger.info(f"✅ Job {job_name} 已完成，添加最终完成日志")
                    
                    # 更新UI的execution_status
                    st.session_state.execution_status = ui_execution_status
                    
                    # 移除重复的强制更新逻辑，避免重复调用模拟函数
                    # console输出已在上面的智能执行状态同步中处理
                    
                    # 模拟智能执行的状态转换：当某个客户的Job完成后，启动下一个Job
                    if 'execution_status' in st.session_state:
                        ui_execution_status = st.session_state.execution_status
                        
                        # 按客户分组检查状态转换
                        jobs_by_customer = {}
                        for job_name, job_status in ui_execution_status.items():
                            if job_status.get('is_smart_execution', False):
                                customer = job_status.get('customer_name', 'Unknown')
                                if customer not in jobs_by_customer:
                                    jobs_by_customer[customer] = []
                                jobs_by_customer[customer].append((job_name, job_status))
                        
                        # 检查每个客户组的状态转换
                        for customer, jobs in jobs_by_customer.items():
                            # 按deployment_order排序
                            sorted_jobs = sorted(jobs, key=lambda x: x[1].get('job_data', {}).get('deployment_order', 0))
                            
                            # 检查是否有Job可以从PENDING转为RUNNING
                            has_running = any(job_status.get('status') == 'RUNNING' for _, job_status in sorted_jobs)
                            
                            if not has_running:  # 如果该客户组没有运行中的Job
                                # 找到第一个PENDING的Job并启动它
                                for job_name, job_status in sorted_jobs:
                                    if job_status.get('status') == 'PENDING':
                                        old_status = job_status.get('status')
                                        job_status['status'] = 'RUNNING'
                                        
                                        # 添加状态转换日志
                                        if 'logs' not in job_status:
                                            job_status['logs'] = []
                                        job_status['logs'].append(
                                            f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 智能执行状态更新: {old_status} → RUNNING"
                                        )
                                        job_status['logs'].append(
                                            f"[{datetime.now().strftime('%H:%M:%S')}] 🚀 开始模拟执行流程..."
                                        )
                                        
                                        logger.info(f"🔄 自动启动下一个Job: {job_name} (客户: {customer})")
                                        break
                        
                        # 更新状态
                        st.session_state.execution_status = ui_execution_status
                    
                    # 更新workflow状态
                    if execution_summary.get("all_completed"):
                        workflow_manager.update_workflow_step("completed")
                    
                    smart_execution_refreshed = True
                    logger.info("✅ 智能执行状态刷新完成")
        except Exception as e:
            logger.warning(f"获取智能执行状态失败，将尝试传统方式: {e}")

    # 传统执行状态刷新逻辑（只处理非智能执行的Jobs）
    if 'execution_status' not in st.session_state:
        if not smart_execution_refreshed:
            logger.warning("⚠️ 没有任何执行状态数据，跳过刷新")
        return

    execution_status = st.session_state.execution_status
    logger.info(f"🔄 开始刷新执行状态，共 {len(execution_status)} 个Jobs")

    # 统计并分类Jobs，避免重复处理智能执行的Jobs
    real_jobs = []
    simulation_jobs = []
    smart_execution_jobs = []
    
    for job_name, status_info in execution_status.items():
        execution_mode = status_info.get('execution_mode', 'simulation')
        is_smart_execution = status_info.get('is_smart_execution', False)
        
        if is_smart_execution:
            smart_execution_jobs.append((job_name, status_info))
            # 智能执行的Jobs在上面已经处理过了，这里跳过
            continue
            
        if execution_mode == 'real':
            real_jobs.append((job_name, status_info))
        else:
            simulation_jobs.append((job_name, status_info))
    
    logger.info(f"📊 Jobs分类 - 真实: {len(real_jobs)}, 传统模拟: {len(simulation_jobs)}, 智能执行: {len(smart_execution_jobs)} (已处理)")

    # 处理真实执行的Jobs
    if real_jobs:
        logger.info("🔗 刷新真实Jenkins Jobs状态")
        refresh_real_jenkins_jobs(real_jobs)

    # 处理传统模拟执行的Jobs（确保不影响智能执行的模拟Jobs）
    if simulation_jobs:
        logger.info("🎭 刷新传统模拟Jobs状态")
        for job_name, status_info in simulation_jobs:
            simulate_job_execution(job_name, status_info)

def simulate_job_execution(job_name: str, status_info: Dict[str, Any]):
    """模拟Job执行，生成console输出和状态更新"""
    import random
    
    if 'logs' not in status_info:
        status_info['logs'] = []
    
    current_logs = status_info['logs']
    current_status = status_info.get('status', 'PENDING')
    
    # 如果Job还没开始运行，启动它
    if current_status == 'PENDING':
        status_info['status'] = 'RUNNING'
        if not status_info.get('build_number'):
            status_info['build_number'] = random.randint(100, 999)
        
        # 添加启动日志
        current_logs.extend([
            f"[{datetime.now().strftime('%H:%M:%S')}] 🎭 模拟执行模式启动",
            f"[{datetime.now().strftime('%H:%M:%S')}] 🚀 开始执行Job: {job_name}",
            f"[{datetime.now().strftime('%H:%M:%S')}] 🔢 模拟Build号: {status_info['build_number']}"
        ])
        return
    
    # 如果Job正在运行，添加执行过程日志
    if current_status == 'RUNNING':
        # 检查最近是否添加了日志，避免过于频繁
        if len(current_logs) > 3:
            last_log = current_logs[-1]
            # 检查时间间隔，控制模拟速度
            try:
                import re
                time_match = re.search(r'\[(\d{2}:\d{2}:\d{2})\]', last_log)
                if time_match:
                    last_time_str = time_match.group(1)
                    last_time = datetime.strptime(last_time_str, '%H:%M:%S').time()
                    current_time = datetime.now().time()
                    
                    # 计算时间差（秒）
                    last_seconds = last_time.hour * 3600 + last_time.minute * 60 + last_time.second
                    current_seconds = current_time.hour * 3600 + current_time.minute * 60 + current_time.second
                    time_diff = current_seconds - last_seconds
                    
                    # 如果时间差小于3秒，不添加新日志
                    if time_diff < 3:
                        return
            except:
                pass  # 如果解析失败，继续添加日志
        
        # 模拟执行过程的不同阶段
        execution_stages = [
            "🔧 Setting up build environment...",
            "📥 Downloading dependencies...",
            "🔨 Compiling source code...",
            "🧪 Running unit tests...",
            "📦 Creating deployment package...",
            "☁️ Uploading artifacts...",
            "🚀 Deploying to target environment...",
            "⚙️ Configuring services...",
            "🔄 Restarting application...",
            "🏥 Running health checks...",
            "📊 Validating deployment...",
            "✅ Deployment completed successfully"
        ]
        
        # 根据当前日志数量决定添加哪个阶段的日志
        effective_logs = len(current_logs) - 3  # 减去初始日志
        
        if 0 <= effective_logs < len(execution_stages):
            new_log = f"[{datetime.now().strftime('%H:%M:%S')}] {execution_stages[effective_logs]}"
            current_logs.append(new_log)
            
            # 添加一些随机的详细信息
            if effective_logs == 2:  # 编译阶段
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📝 Processed {random.randint(50, 200)} source files")
            elif effective_logs == 4:  # 打包阶段
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📦 Package size: {random.randint(10, 50)}MB")
            elif effective_logs == 6:  # 部署阶段
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🌐 Target environment: Production")
            elif effective_logs == len(execution_stages) - 1:  # 最后阶段
                # 模拟执行完成
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 Job execution completed!")
                
                # 随机决定成功或失败（90%成功率）
                if random.random() < 0.9:
                    status_info['status'] = 'SUCCESS'
                    current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Status: SUCCESS")
                else:
                    status_info['status'] = 'FAILURE'
                    current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Status: FAILURE")
                    current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🐛 Error: Simulated failure for testing")
        
        elif effective_logs >= len(execution_stages):
            # 如果还在运行但已经超过了所有阶段，随机完成
            if random.random() < 0.3:  # 30%概率在这次刷新时完成
                status_info['status'] = 'SUCCESS'
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 模拟执行完成！")
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Status: SUCCESS")
    
    status_info['logs'] = current_logs

def refresh_real_jenkins_jobs(real_jobs: List[Tuple[str, Dict]]):
    """刷新真实Jenkins Jobs状态"""
    logger = logging.getLogger(__name__)
    
    try:
        # 检查Jenkins客户端是否可用
        if 'agent' in st.session_state and st.session_state.agent:
            jenkins_client = st.session_state.agent.jenkins_client
            
            if jenkins_client:
                for job_name, status_info in real_jobs:
                    try:
                        build_number = status_info.get('build_number')
                        current_status = status_info.get('status')
                        
                        if build_number and current_status not in ['SUCCESS', 'FAILURE', 'ABORTED']:
                            # 获取真实的Jenkins状态
                            logger.info(f"📡 获取Job {job_name} Build {build_number} 的状态")
                            
                            # 获取build状态
                            build_result = jenkins_client.get_build_status(job_name, build_number)
                            if build_result["success"]:
                                build_info = build_result
                                new_status = build_info.get('status', 'UNKNOWN')
                                
                                # 更新状态
                                if new_status != current_status:
                                    status_info['status'] = new_status
                                    status_info['logs'].append(
                                        f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 Jenkins状态更新: {new_status}"
                                    )
                                    logger.info(f"✅ Job {job_name} 状态更新: {current_status} -> {new_status}")
                                
                                # 获取Console输出
                                console_result = jenkins_client.get_console_output(job_name, build_number)
                                if console_result["success"]:
                                    console_output = console_result.get('console_output', '')
                                    if console_output:
                                        # 将console输出分割成行，只保留最新的内容
                                        console_lines = console_output.strip().split('\n')
                                        
                                        # 检查是否有新的日志内容
                                        existing_logs = status_info.get('logs', [])
                                        # 过滤掉我们自己添加的状态日志，只保留原始Jenkins日志
                                        jenkins_logs = [log for log in existing_logs 
                                                      if not any(marker in log for marker in ['状态更新:', 'Job已成功触发', 'Queue Item:', 'Build Number:'])]
                                        
                                        # 添加新的Jenkins console输出（限制长度）
                                        if len(console_lines) > len(jenkins_logs):
                                            # 添加时间戳到新的console日志
                                            new_console_lines = console_lines[len(jenkins_logs):]
                                            timestamped_logs = []
                                            for line in new_console_lines[-15:]:  # 只取最新15行
                                                if line.strip():  # 跳过空行
                                                    timestamped_logs.append(line)
                                            
                                            # 合并日志：系统日志 + Jenkins console输出
                                            system_logs = [log for log in existing_logs 
                                                         if any(marker in log for marker in ['状态更新:', 'Job已成功触发', 'Queue Item:', 'Build Number:'])]
                                            status_info['logs'] = system_logs + timestamped_logs
                                
                                # 如果job完成，添加完成日志
                                if new_status in ['SUCCESS', 'FAILURE', 'ABORTED'] and current_status not in ['SUCCESS', 'FAILURE', 'ABORTED']:
                                    status_info['logs'].append(
                                        f"[{datetime.now().strftime('%H:%M:%S')}] 🏁 Jenkins Job执行完成: {new_status}"
                                    )
                                    
                                    # 获取执行时长
                                    duration = build_info.get('duration', 0)
                                    if duration > 0:
                                        duration_str = f"{duration // 1000}秒"
                                        status_info['logs'].append(
                                            f"[{datetime.now().strftime('%H:%M:%S')}] ⏱️ 执行时长: {duration_str}"
                                        )
                        
                        elif not build_number and current_status == 'PENDING':
                            # 对于PENDING状态且没有build_number的job，检查是否已经开始
                            queue_item_number = status_info.get('queue_item_number')
                            if queue_item_number:
                                # 检查队列状态，看是否已经开始执行
                                try:
                                    queue_item = jenkins_client.server.get_queue_item(queue_item_number)
                                    if 'executable' in queue_item:
                                        build_number = queue_item['executable']['number']
                                        status_info['build_number'] = build_number
                                        status_info['status'] = 'RUNNING'
                                        status_info['logs'].append(
                                            f"[{datetime.now().strftime('%H:%M:%S')}] 🚀 Jenkins Job开始执行，Build号: {build_number}"
                                        )
                                        logger.info(f"✅ Job {job_name} 开始执行，Build号: {build_number}")
                                except Exception as e:
                                    logger.warning(f"⚠️ 检查队列状态失败: {e}")
                    
                    except Exception as e:
                        logger.error(f"❌ 刷新Job {job_name} 状态失败: {e}")
                        # 对于真实执行出错的情况，不回退到模拟，而是保持当前状态
                        status_info['logs'].append(
                            f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ Jenkins状态刷新失败: {str(e)}"
                        )
            else:
                logger.warning("⚠️ Jenkins客户端不可用，真实执行Jobs无法刷新状态")
                # 对于真实Jobs但Jenkins不可用的情况，添加警告日志
                for job_name, status_info in real_jobs:
                    if status_info.get('status') not in ['SUCCESS', 'FAILURE', 'ABORTED']:
                        status_info['logs'].append(
                            f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ Jenkins连接不可用，无法获取最新状态"
                        )
        else:
            logger.warning("⚠️ Agent不可用，真实执行Jobs无法刷新状态")
    
    except Exception as e:
        logger.error(f"❌ 刷新真实Jobs状态时发生异常: {e}")

def stop_job(job_name: str):
    """停止Job执行"""
    if 'execution_status' in st.session_state and job_name in st.session_state.execution_status:
        st.session_state.execution_status[job_name]['status'] = 'ABORTED'
        st.session_state.execution_status[job_name]['logs'].append(
            f"[{datetime.now().strftime('%H:%M:%S')}] Job aborted by user"
        )
        st.warning(f"已停止Job: {job_name}")
        st.rerun()

def display_workflow_navigation():
    """显示工作流导航"""
    logger = logging.getLogger(__name__)
    current_step = workflow_manager.get_current_step()

    logger.info(f"🧭 当前步骤: {current_step}")
    logger.info(f"📋 执行确认状态: {st.session_state.get('show_execution_confirmation', False)}")
    logger.info(f"📊 执行状态存在: {'execution_status' in st.session_state}")

    # 创建步骤导航
    steps = workflow_manager.WORKFLOW_STEPS
    logger.info(f"📋 总步骤数: {len(steps)}")
    for i, step in enumerate(steps):
        logger.info(f"  步骤{i+1}: {step['key']} - {step['name']}")

    # 使用固定宽度的列来确保所有步骤都能显示
    cols = st.columns([1, 1, 1, 1, 1])  # 5个等宽列

    for i, step in enumerate(steps):
        with cols[i]:
            is_current = workflow_manager.is_step_current(step["key"], current_step)
            is_completed = workflow_manager.is_step_completed(step["key"], current_step)

            logger.info(f"🔍 步骤{i+1} {step['key']}: current={is_current}, completed={is_completed}")

            if is_completed:
                st.markdown(f"""
                <div style="
                    background: #28a745;
                    color: white;
                    padding: 10px;
                    border-radius: 8px;
                    text-align: center;
                    margin: 5px 0;
                ">
                    <div style="font-size: 1.2em;">✅</div>
                    <div style="font-size: 0.8em; margin-top: 5px;">{step['name']}</div>
                </div>
                """, unsafe_allow_html=True)
            elif is_current:
                st.markdown(f"""
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 10px;
                    border-radius: 8px;
                    text-align: center;
                    margin: 5px 0;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                    animation: pulse 2s infinite;
                ">
                    <div style="font-size: 1.2em;">{step['icon']}</div>
                    <div style="font-size: 0.8em; margin-top: 5px; font-weight: bold;">{step['name']}</div>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="
                    background: #f8f9fa;
                    color: #6c757d;
                    padding: 10px;
                    border-radius: 8px;
                    text-align: center;
                    margin: 5px 0;
                    border: 2px dashed #dee2e6;
                ">
                    <div style="font-size: 1.2em;">⏳</div>
                    <div style="font-size: 0.8em; margin-top: 5px;">{step['name']}</div>
                </div>
                """, unsafe_allow_html=True)

def display_smart_execution_monitoring(group_status: Dict, execution_summary: Dict, is_completed: bool):
    """显示智能执行管理器的监控界面"""
    logger = logging.getLogger(__name__)
    logger.info("🧠 显示智能执行管理器监控界面")
    
    with st.container(border=True):
        # 根据完成状态调整标题
        if is_completed:
            st.subheader("📋 智能执行结果总览")
        else:
            st.subheader("🧠 智能执行监控")
            st.caption("📊 按客户分组，同一客户内的服务按部署顺序串行执行")

        # 自动刷新控制（只在monitoring状态下启用）
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            auto_refresh = st.checkbox("自动刷新", value=not is_completed, disabled=is_completed)
        with col2:
            # 智能执行的模拟Jobs使用更短的刷新间隔
            running_jobs = execution_summary.get("running_jobs", 0)
            if running_jobs > 0:
                refresh_interval = st.selectbox("刷新间隔", [2, 3, 5, 10], index=0, disabled=is_completed, 
                                              help="智能执行使用较短间隔以显示逐步进度")
            else:
                refresh_interval = st.selectbox("刷新间隔", [5, 10, 15, 30], index=0, disabled=is_completed)
        with col3:
            if st.button("🔄 手动刷新", use_container_width=True, disabled=is_completed):
                refresh_execution_status()

        st.markdown("---")
        
        # 使用智能执行状态组件
        display_smart_execution_status(group_status, execution_summary)
        
        # 显示详细的Job状态和Console输出
        st.markdown("---")
        st.markdown("### 📋 Job执行详情")
        
        # 获取当前execution_status中的详细信息
        if 'execution_status' in st.session_state:
            execution_status = st.session_state.execution_status
            
            # 只显示智能执行的Jobs
            smart_jobs = {name: status for name, status in execution_status.items() 
                         if status.get('is_smart_execution', False)}
            
            if smart_jobs:
                st.markdown(f"**当前有 {len(smart_jobs)} 个智能执行Jobs:**")
                
                # 按客户分组显示
                jobs_by_customer = {}
                for job_name, status_info in smart_jobs.items():
                    customer = status_info.get('customer_name', '未知客户')
                    if customer not in jobs_by_customer:
                        jobs_by_customer[customer] = []
                    jobs_by_customer[customer].append((job_name, status_info))
                
                # 显示每个客户的Jobs
                for customer, jobs in jobs_by_customer.items():
                    st.markdown(f"#### 🏢 {customer}")
                    
                    for job_name, status_info in jobs:
                        display_job_monitoring(job_name, status_info)
            else:
                st.info("当前没有智能执行的Jobs数据")
        else:
            st.warning("没有找到execution_status数据")
        
        # 自动刷新逻辑
        all_completed = execution_summary.get("all_completed", False)
        running_jobs = execution_summary.get("running_jobs", 0)
        
        if auto_refresh and not all_completed and not is_completed:
            # 使用session state来跟踪上次刷新时间
            current_time = time.time()
            last_refresh = st.session_state.get('last_refresh_time', 0)
            
            # 如果是第一次进入或者达到刷新间隔，立即刷新
            if last_refresh == 0 or current_time - last_refresh >= refresh_interval:
                st.session_state.last_refresh_time = current_time
                refresh_execution_status()
                st.rerun()
            else:
                # 显示下次刷新倒计时
                remaining_time = refresh_interval - (current_time - last_refresh)
                st.info(f"⏱️ 下次自动刷新: {int(remaining_time)}秒后")
                time.sleep(1)  # 短暂等待
                st.rerun()
        elif auto_refresh and all_completed:
            # 所有Job都已完成，显示提示信息
            st.success("🎉 所有执行组已完成，自动刷新已停止")
        elif is_completed or all_completed:
            # 工作流已完成，显示最终状态
            completed_jobs = execution_summary.get("completed_jobs", 0)
            failed_jobs = execution_summary.get("failed_jobs", 0)
            total_jobs = execution_summary.get("total_jobs", 0)
            
            if failed_jobs == 0:
                st.toast("🎉 工作流完成！所有Jobs执行成功！", icon="✅")
                st.success(f"🎉 所有{total_jobs}个Jobs执行成功！工作流已完成")
            else:
                st.error(f"⚠️ {failed_jobs}个Jobs执行失败，{completed_jobs}个Jobs成功，请检查详细日志")

def simulate_console_output_for_smart_execution(job_name: str, status_info: Dict[str, Any], execution_manager=None):
    """为智能执行的模拟Jobs生成真实的console输出"""
    import random
    import logging
    logger = logging.getLogger(__name__)
    
    if 'logs' not in status_info:
        status_info['logs'] = []
    
    current_logs = status_info['logs']
    current_status = status_info.get('status', 'PENDING')
    
    logger.info(f"🎭 模拟Job {job_name} console输出，当前状态: {current_status}, 当前日志数: {len(current_logs)}")
    
    # 如果Job已经完成，不再处理
    if current_status in ['SUCCESS', 'FAILURE', 'ABORTED']:
        logger.info(f"   - ⏭️ Job {job_name} 已完成 ({current_status})，跳过console输出更新")
        return
    
    # 如果是PENDING状态，显示等待信息
    if current_status == 'PENDING':
        # 添加等待日志，但不要过多且避免重复
        last_pending_idx = status_info.get('_pending_idx', -1)
        if last_pending_idx < 7:  # 最多8条等待日志
            next_idx = last_pending_idx + 1
            pending_messages = [
                "⏳ 等待执行队列中...",
                "🔍 检查前置依赖任务...",
                "🔄 持续监控依赖任务状态...",
                "📋 等待资源分配...",
                "🛡️ 验证执行权限中...",
                "⏱️ 等待执行时间窗口...",
                "🔍 检查系统资源可用性...",
                "⚡ 准备启动执行..."
            ]
            if next_idx < len(pending_messages):
                status_info['_pending_idx'] = next_idx  # 记录已生成索引
                new_log = f"[{datetime.now().strftime('%H:%M:%S')}] {pending_messages[next_idx]}"
                current_logs.append(new_log)
                logger.info(f"   - 添加等待日志: {new_log}")
        status_info['logs'] = current_logs
        return
    
    # 如果是RUNNING状态，显示执行进度
    if current_status == 'RUNNING':
        stage_logs = [
            "🔧 Setting up execution environment...",
            "📥 Checking out code from repository...",
            "🔨 Building application...",
            "🧪 Running unit tests...", 
            "📦 Creating deployment package...",
            "☁️ Uploading to cloud storage...",
            "🚀 Deploying to target environment...",
            "⚙️ Updating configuration files...",
            "🔄 Restarting application services...",
            "🏥 Running health checks...",
            "📊 Verifying deployment metrics...",
            "🧹 Cleaning up temporary files...",
            "✅ Deployment validation completed"
        ]
        stage_idx = status_info.get('_stage_idx', -1)  # 最近一次生成的stage索引
        if stage_idx < len(stage_logs)-1:
            next_idx = stage_idx + 1
            status_info['_stage_idx'] = next_idx
            new_log = f"[{datetime.now().strftime('%H:%M:%S')}] {stage_logs[next_idx]}"
            current_logs.append(new_log)
            logger.info(f"   - 添加执行阶段日志 {next_idx+1}/{len(stage_logs)}: {stage_logs[next_idx]}")
            # 关键步骤附加详情
            if next_idx == 2:
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📝 Compiling {random.randint(50,200)} source files...")
            elif next_idx == 6:
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] 📍 Target: Production Environment")
            elif next_idx == 9:
                current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ All health endpoints responding")
        else:
            # 所有stage完成后添加收尾日志一次性完成
            if status_info.get('_finished') is not True:
                finish_logs = [
                    f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 Job execution completed successfully!",
                    f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 准备标记任务为完成状态...",
                ]
                current_logs.extend(finish_logs)
                status_info['_finished'] = True
                status_info['status'] = 'SUCCESS'
                logger.info(f"🎉 模拟Job {job_name} 执行完成，设置为SUCCESS")
    status_info['logs'] = current_logs

def single_page_workflow():
    """单页面工作流主函数"""
    logger = logging.getLogger(__name__)
    logger.info("🌊 单页面工作流启动")

    st.title("🌊 智能发布工作流")
    st.markdown("一个连续的、直观的发布管理流程")

    # 添加CSS动画
    st.markdown("""
    <style>
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .workflow-container {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        border-left: 5px solid #667eea;
    }

    .step-completed {
        opacity: 0.7;
        background: #f0f8f0 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # 显示工作流导航
    display_workflow_navigation()

    # 显示整体进度
    current_step = workflow_manager.get_current_step()
    progress_percent = workflow_manager.get_progress_percentage(current_step)
    st.progress(progress_percent, text=f"整体进度: {int(progress_percent * 100)}%")

    # 控制按钮
    col1, col2, col3 = st.columns([2, 1, 1])
    with col2:
        if st.button("🔄 重置工作流", use_container_width=True):
            workflow_manager.reset_workflow(preserve_history=True)
            st.rerun()
    with col3:
        if st.button("💾 保存状态", use_container_width=True):
            st.success("状态已保存")

    st.markdown("---")

    # 按顺序显示所有步骤
    step_1_plan_query()
    step_2_plan_approval()
    step_3_execution_management()
    step_4_execution_confirmation()
    step_5_monitoring()

    # 完成提示
    if 'execution_status' in st.session_state:
        execution_status = st.session_state.execution_status
        if all(status.get('status') in ['SUCCESS', 'FAILURE', 'ABORTED']
               for status in execution_status.values()):

            success_count = sum(1 for status in execution_status.values()
                              if status.get('status') == 'SUCCESS')
            total_count = len(execution_status)

            st.markdown("---")

            if success_count == total_count:
                st.toast("🎉 工作流完成！所有Jobs执行成功！", icon="✅")
                st.markdown(f"""
                <div style="
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    text-align: center;
                    margin: 20px 0;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                ">
                    <h2 style="margin: 0; color: white;">🎉 工作流完成！</h2>
                    <p style="margin: 10px 0 0 0; font-size: 1.1em;">
                        所有 {total_count} 个Jobs执行成功！
                    </p>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="
                    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    text-align: center;
                    margin: 20px 0;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                ">
                    <h2 style="margin: 0; color: white;">⚠️ 工作流完成</h2>
                    <p style="margin: 10px 0 0 0; font-size: 1.1em;">
                        {success_count}/{total_count} 个Jobs执行成功
                    </p>
                </div>
                """, unsafe_allow_html=True)

            col1, col2, col3 = st.columns([1, 1, 1])
            with col2:
                if st.button("🆕 开始新的工作流", type="primary", use_container_width=True):
                    workflow_manager.reset_workflow(preserve_history=True)
                    st.rerun()
