"""
工作流状态管理器
管理和跟踪工作流的执行状态
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import datetime

class WorkflowManager:
    """工作流状态管理器"""
    
    # 定义工作流步骤
    WORKFLOW_STEPS = [
        {
            "key": "plan_query",
            "name": "计划查询",
            "icon": "📝",
            "description": "查询和生成发布计划"
        },
        {
            "key": "plan_approval",
            "name": "计划审批",
            "icon": "👀",
            "description": "审核和确认发布计划"
        },
        {
            "key": "execution",
            "name": "执行管理",
            "icon": "🚀",
            "description": "选择要执行的Jenkins Jobs"
        },
        {
            "key": "execution_confirmation",
            "name": "执行确认",
            "icon": "🔍",
            "description": "确认Job参数和执行顺序"
        },
        {
            "key": "monitoring",
            "name": "状态监控",
            "icon": "📊",
            "description": "监控执行状态和结果"
        }
    ]
    
    @classmethod
    def get_current_step(cls) -> str:
        """获取当前工作流步骤"""
        import logging
        logger = logging.getLogger(__name__)

        # 检查是否在执行确认阶段
        show_confirmation = st.session_state.get('show_execution_confirmation', False)
        has_execution_status = 'execution_status' in st.session_state

        logger.info(f"🔍 工作流状态检查:")
        logger.info(f"  - 显示执行确认: {show_confirmation}")
        logger.info(f"  - 存在执行状态: {has_execution_status}")

        if show_confirmation:
            logger.info("📋 当前步骤: execution_confirmation (执行确认)")
            return "execution_confirmation"

        # 检查是否在监控阶段 - 这个判断优先级最高
        if has_execution_status:
            execution_status = st.session_state.execution_status
            all_completed = all(status.get('status') in ['SUCCESS', 'FAILURE', 'ABORTED']
                               for status in execution_status.values())
            logger.info(f"  - 所有Jobs已完成: {all_completed}")

            if all_completed:
                logger.info("🏁 当前步骤: completed (已完成)")
                # 清理手动设置的步骤状态，避免冲突
                if 'workflow_step' in st.session_state:
                    del st.session_state['workflow_step']
                return "completed"
            logger.info("📊 当前步骤: monitoring (监控中)")
            # 确保手动步骤状态与实际状态一致
            st.session_state['workflow_step'] = "monitoring"
            return "monitoring"

        # 检查是否有工作流步骤状态（手动设置的）- 降低优先级
        manual_step = st.session_state.get('workflow_step')
        if manual_step:
            logger.info(f"🎯 手动设置的步骤: {manual_step}")
            
            # 验证手动设置的步骤是否有效
            valid_steps = [step["key"] for step in cls.WORKFLOW_STEPS]
            if manual_step in valid_steps:
                # 特殊处理：如果手动设置为monitoring但没有执行状态，重置为合适的步骤
                if manual_step == "monitoring" and not has_execution_status:
                    logger.warning("⚠️ 手动设置为monitoring但无执行状态，重置步骤")
                    # 基于现有状态重新判断
                    if 'deployment_plan' in st.session_state and st.session_state.deployment_plan:
                        workflow_state = st.session_state.deployment_plan.get("workflow_state", "plan_generated")
                        if workflow_state == "plan_approved":
                            st.session_state['workflow_step'] = "execution"
                            logger.info("📝 重置为执行管理步骤")
                            return "execution"
                        else:
                            st.session_state['workflow_step'] = "plan_approval"
                            logger.info("📝 重置为计划审批步骤")
                            return "plan_approval"
                    else:
                        st.session_state['workflow_step'] = "plan_query"
                        logger.info("📝 重置为计划查询步骤")
                        return "plan_query"
                
                logger.info(f"📊 使用手动设置的步骤: {manual_step}")
                return manual_step
            else:
                logger.warning(f"⚠️ 无效的手动步骤: {manual_step}，删除该设置")
                del st.session_state['workflow_step']

        # 从会话状态判断当前步骤
        if 'deployment_plan' not in st.session_state or not st.session_state.deployment_plan:
            logger.info("📝 当前步骤: plan_query (计划查询)")
            st.session_state['workflow_step'] = "plan_query"  # 确保手动状态一致
            return "plan_query"

        deployment_plan = st.session_state.deployment_plan
        workflow_state = deployment_plan.get("workflow_state", "plan_generated")
        logger.info(f"  - 工作流状态: {workflow_state}")

        # 根据工作流状态映射到步骤
        state_mapping = {
            "start": "plan_query",
            "plan_generated": "plan_approval",
            "plan_approved": "execution",
            "execution_ready": "execution",
            "executing": "monitoring",
            "monitoring": "monitoring",
            "completed": "monitoring"
        }

        current_step = state_mapping.get(workflow_state, "plan_query")
        logger.info(f"🎯 映射的当前步骤: {current_step}")
        
        # 同步手动状态
        st.session_state['workflow_step'] = current_step
        
        return current_step
    
    @classmethod
    def get_step_index(cls, step_key: str) -> int:
        """获取步骤索引"""
        for i, step in enumerate(cls.WORKFLOW_STEPS):
            if step["key"] == step_key:
                return i
        return 0
    
    @classmethod
    def get_step_info(cls, step_key: str) -> Dict[str, Any]:
        """获取步骤信息"""
        for step in cls.WORKFLOW_STEPS:
            if step["key"] == step_key:
                return step
        return cls.WORKFLOW_STEPS[0]
    
    @classmethod
    def get_progress_percentage(cls, current_step: str) -> float:
        """获取进度百分比 - 基于已完成的步骤数量计算"""
        completed_steps = 0
        
        # 计算实际已完成的步骤数量
        for step in cls.WORKFLOW_STEPS:
            if cls.is_step_completed(step["key"], current_step):
                completed_steps += 1
        
        # 如果当前步骤有部分进展，可以加上部分进度
        current_step_progress = 0
        
        # 根据当前步骤的实际状态添加部分进度
        if current_step == "plan_query":
            # 如果有部署计划数据，说明查询有进展
            if 'deployment_plan' in st.session_state and st.session_state.deployment_plan:
                current_step_progress = 0.8  # 查询步骤80%完成
        elif current_step == "plan_approval":
            # 如果计划已审批，说明该步骤完成
            if 'deployment_plan' in st.session_state:
                workflow_state = st.session_state.deployment_plan.get("workflow_state", "")
                if workflow_state == "plan_approved":
                    current_step_progress = 1.0  # 审批步骤100%完成
                elif st.session_state.deployment_plan:
                    current_step_progress = 0.5  # 有计划数据，审批进行中
        elif current_step == "execution":
            # 如果有选中的jobs，说明执行管理有进展
            if 'selected_jobs_for_execution' in st.session_state:
                current_step_progress = 0.8  # 执行管理80%完成
            elif 'deployment_plan' in st.session_state:
                workflow_state = st.session_state.deployment_plan.get("workflow_state", "")
                if workflow_state == "plan_approved":
                    current_step_progress = 0.3  # 刚进入执行阶段
        elif current_step == "execution_confirmation":
            # 确认阶段，如果显示确认界面说明有进展
            if st.session_state.get('show_execution_confirmation', False):
                current_step_progress = 0.7  # 确认界面显示，70%完成
        elif current_step == "monitoring":
            # 监控阶段，根据jobs执行状态计算进度
            if 'execution_status' in st.session_state and st.session_state.execution_status:
                execution_status = st.session_state.execution_status
                total_jobs = len(execution_status)
                completed_jobs = sum(1 for status in execution_status.values()
                                   if status.get('status') in ['SUCCESS', 'FAILURE', 'ABORTED'])
                if total_jobs > 0:
                    # 监控阶段的进度应该是：基础进度 + 当前步骤内的执行进度
                    job_progress = completed_jobs / total_jobs
                    current_step_progress = 0.2 + (job_progress * 0.8)  # 监控步骤从20%开始，到100%结束
                else:
                    current_step_progress = 0.2  # 监控刚开始
            else:
                current_step_progress = 0.1  # 监控刚开始
        elif current_step == "completed":
            # 已完成，当前步骤贡献100%
            current_step_progress = 1.0
            completed_steps = len(cls.WORKFLOW_STEPS) - 1  # 所有步骤都完成了
        
        # 总进度 = 已完成步骤数 + 当前步骤进度
        total_progress = (completed_steps + current_step_progress) / len(cls.WORKFLOW_STEPS)
        
        # 确保进度在0-1之间
        return max(0.0, min(1.0, total_progress))
    
    @classmethod
    def is_step_completed(cls, step_key: str, current_step: str) -> bool:
        """判断步骤是否已完成"""
        import logging
        logger = logging.getLogger(__name__)
        
        # 如果工作流已完成，除了最后一个步骤外，其他都应该显示为完成
        if current_step == "completed":
            if step_key == "monitoring":
                return True  # 监控步骤也算完成
            else:
                return step_key != "monitoring"  # 除了monitoring外的其他步骤都完成
        
        # 特殊处理各个步骤的完成条件
        if step_key == "plan_query":
            # 计划查询步骤：有部署计划数据就算完成
            completed = 'deployment_plan' in st.session_state and st.session_state.deployment_plan
            if completed and current_step not in ["plan_query"]:
                return True
            
        elif step_key == "plan_approval":
            # 计划审批步骤：工作流状态为plan_approved就算完成
            if 'deployment_plan' in st.session_state and st.session_state.deployment_plan:
                workflow_state = st.session_state.deployment_plan.get("workflow_state", "")
                completed = workflow_state == "plan_approved"
                if completed and current_step not in ["plan_query", "plan_approval"]:
                    return True
                    
        elif step_key == "execution":
            # 执行管理步骤：有选中的jobs或进入执行确认/监控阶段就算完成
            has_selected_jobs = 'selected_jobs_for_execution' in st.session_state
            has_execution_status = 'execution_status' in st.session_state
            in_later_stages = current_step in ["execution_confirmation", "monitoring", "completed"]
            
            completed = (has_selected_jobs or has_execution_status) and in_later_stages
            if completed:
                return True
                
        elif step_key == "execution_confirmation":
            # 执行确认步骤：有执行状态就算完成
            has_execution_status = 'execution_status' in st.session_state
            in_monitoring_or_completed = current_step in ["monitoring", "completed"]
            
            completed = has_execution_status and in_monitoring_or_completed
            if completed:
                return True
                
        elif step_key == "monitoring":
            # 监控步骤：只有在completed状态下才算完成
            return current_step == "completed"
        
        # 传统的基于步骤顺序的判断（作为后备）
        step_index = cls.get_step_index(step_key)
        current_index = cls.get_step_index(current_step)
        traditional_completed = step_index < current_index
        
        logger.debug(f"步骤完成检查: {step_key}, 当前:{current_step}, 传统判断:{traditional_completed}")
        
        return traditional_completed
    
    @classmethod
    def is_step_current(cls, step_key: str, current_step: str) -> bool:
        """判断是否为当前步骤"""
        return step_key == current_step
    
    @classmethod
    def get_execution_status_summary(cls) -> Optional[Dict[str, Any]]:
        """获取执行状态摘要"""
        if 'execution_status' not in st.session_state or not st.session_state.execution_status:
            return None
        
        execution_status = st.session_state.execution_status
        
        total_jobs = len(execution_status)
        if total_jobs == 0:
            return None
        
        # 统计各种状态的job数量
        status_counts = {
            "SUCCESS": 0,
            "RUNNING": 0, 
            "FAILURE": 0,
            "ABORTED": 0,
            "PENDING": 0
        }
        
        for job_status in execution_status.values():
            status = job_status.get('status', 'PENDING')
            if status in status_counts:
                status_counts[status] += 1
            else:
                status_counts["PENDING"] += 1
        
        completed_jobs = status_counts["SUCCESS"] + status_counts["FAILURE"] + status_counts["ABORTED"]
        
        return {
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "success_count": status_counts["SUCCESS"],
            "running_count": status_counts["RUNNING"],
            "failure_count": status_counts["FAILURE"] + status_counts["ABORTED"],
            "pending_count": status_counts["PENDING"],
            "completion_percentage": completed_jobs / total_jobs if total_jobs > 0 else 0
        }
    
    @classmethod
    def update_workflow_step(cls, step_key: str):
        """更新工作流步骤"""
        st.session_state.workflow_step = step_key
        
        # 记录步骤变更时间
        if 'workflow_history' not in st.session_state:
            st.session_state.workflow_history = []
        
        st.session_state.workflow_history.append({
            "step": step_key,
            "timestamp": datetime.now(),
            "step_name": cls.get_step_info(step_key)["name"]
        })
    
    @classmethod
    def get_workflow_history(cls) -> List[Dict[str, Any]]:
        """获取工作流历史"""
        return st.session_state.get('workflow_history', [])
    
    @classmethod
    def reset_workflow(cls, preserve_history: bool = False):
        """重置工作流状态
        
        Args:
            preserve_history: 是否保留工作流历史记录
        """
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info("🔄 开始重置工作流状态...")
        
        # 清除工作流相关的会话状态 - 更全面的清理
        workflow_keys = [
            'workflow_step',
            'deployment_plan',
            'execution_status',
            'raw_jenkins_jobs',
            'confirming_execution',
            'jobs_to_execute',
            'selected_jobs_for_execution',
            'show_execution_confirmation',
            'last_refresh_time',
            # 添加更多需要清理的状态
            'execution_mode',
            'current_executing_job',
            'current_build_number',
            'waiting_for_completion',
            'jenkins_jobs_cache',
            'execution_results',
            'workflow_running',
            'workflow_status',
            'workflow_errors'
        ]
        
        # 如果不保留历史，也清除历史记录
        if not preserve_history:
            workflow_keys.append('workflow_history')
        
        removed_keys = []
        for key in workflow_keys:
            if key in st.session_state:
                del st.session_state[key]
                removed_keys.append(key)
        
        # 特殊处理：重置Agent的工作流状态
        if 'agent' in st.session_state and st.session_state.agent:
            try:
                # 重置Agent内部的工作流状态
                if hasattr(st.session_state.agent, 'current_state'):
                    st.session_state.agent.current_state = None
                    logger.info("🔧 重置Agent工作流状态")
                
                # 重置智能执行管理器
                if (hasattr(st.session_state.agent, 'workflow') and 
                    hasattr(st.session_state.agent.workflow, 'execution_manager')):
                    st.session_state.agent.workflow.execution_manager = None
                    logger.info("🧠 清除智能执行管理器")
                    
            except Exception as e:
                logger.warning(f"重置Agent状态时出现警告: {e}")
        
        logger.info(f"✅ 工作流重置完成，清除了 {len(removed_keys)} 个状态: {removed_keys}")
        
        # 确保设置为初始步骤
        st.session_state['workflow_step'] = "plan_query"
        logger.info("📝 工作流步骤重置为: plan_query")
        
        # 如果保留历史，添加重置记录
        if preserve_history:
            if 'workflow_history' not in st.session_state:
                st.session_state.workflow_history = []
            
            st.session_state.workflow_history.append({
                "step": "reset",
                "timestamp": datetime.now(),
                "step_name": "工作流重置"
            })
            logger.info("📚 添加重置记录到工作流历史")
    
    @classmethod
    def is_workflow_active(cls) -> bool:
        """判断工作流是否处于活跃状态（有进行中的数据）"""
        return (
            'deployment_plan' in st.session_state or
            'execution_status' in st.session_state or
            'selected_jobs_for_execution' in st.session_state
        )
    
    @classmethod
    def get_next_step_suggestion(cls, current_step: str) -> Optional[str]:
        """获取下一步建议"""
        current_index = cls.get_step_index(current_step)
        
        if current_index < len(cls.WORKFLOW_STEPS) - 1:
            next_step = cls.WORKFLOW_STEPS[current_index + 1]
            return f"下一步: {next_step['icon']} {next_step['name']}"
        else:
            return "工作流已完成"
    
    @classmethod
    def can_proceed_to_next_step(cls, current_step: str) -> bool:
        """判断是否可以进入下一步"""
        if current_step == "plan_query":
            # 需要有部署计划
            return 'deployment_plan' in st.session_state and st.session_state.deployment_plan
        elif current_step == "plan_approval":
            # 需要计划已审批
            if 'deployment_plan' in st.session_state:
                workflow_state = st.session_state.deployment_plan.get("workflow_state", "")
                return workflow_state == "plan_approved"
            return False
        elif current_step == "execution":
            # 需要有执行状态
            return 'execution_status' in st.session_state and st.session_state.execution_status
        else:
            return True

# 全局工作流管理器实例
workflow_manager = WorkflowManager()
