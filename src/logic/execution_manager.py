"""
智能执行管理器
处理Jenkins Jobs的执行顺序，确保同一客户的单租户服务按部署顺序串行执行
"""

import logging
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime
from collections import defaultdict, deque
from enum import Enum

logger = logging.getLogger(__name__)

class JobStatus(Enum):
    """Job执行状态"""
    PENDING = "PENDING"
    READY = "READY"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    ABORTED = "ABORTED"
    BLOCKED = "BLOCKED"  # 被依赖关系阻塞

class ExecutionGroup:
    """执行组 - 代表一个客户的所有服务"""
    
    def __init__(self, customer_name: str, tenant_name: str = ""):
        self.customer_name = customer_name
        self.tenant_name = tenant_name
        self.jobs: List[Dict] = []
        self.current_job_index = 0
        self.group_status = JobStatus.PENDING
        self.group_id = f"{customer_name}_{tenant_name}" if tenant_name else customer_name
    
    def add_job(self, job: Dict):
        """添加Job到执行组"""
        self.jobs.append(job)
        # 按部署顺序排序
        self.jobs.sort(key=lambda x: x.get("deployment_order", 999))
    
    def get_current_job(self) -> Optional[Dict]:
        """获取当前应该执行的Job"""
        if self.current_job_index < len(self.jobs):
            return self.jobs[self.current_job_index]
        return None
    
    def mark_current_job_completed(self, success: bool = True):
        """标记当前Job完成"""
        if success:
            self.current_job_index += 1
        # 如果失败，可以选择停止该组的后续执行或继续
        
    def is_group_completed(self) -> bool:
        """检查组是否全部完成"""
        return self.current_job_index >= len(self.jobs)
    
    def get_pending_jobs(self) -> List[Dict]:
        """获取待执行的Jobs"""
        return self.jobs[self.current_job_index:]
    
    def get_completed_jobs(self) -> List[Dict]:
        """获取已完成的Jobs"""
        return self.jobs[:self.current_job_index]

class SmartExecutionManager:
    """智能执行管理器"""
    
    def __init__(self, jenkins_client=None):
        self.jenkins_client = jenkins_client
        self.execution_groups: Dict[str, ExecutionGroup] = {}
        self.job_status: Dict[str, JobStatus] = {}
        self.job_results: Dict[str, Dict] = {}
        self.execution_log: List[str] = []
        
        # 执行统计
        self.total_jobs = 0
        self.completed_jobs = 0
        self.failed_jobs = 0
        self.running_jobs = 0
        
    def organize_jobs_by_customer(self, jobs: List[Dict]) -> Dict[str, ExecutionGroup]:
        """按客户组织Jobs"""
        groups = {}
        
        for job in jobs:
            customer_name = job.get("customer_name", "Unknown")
            tenant_name = job.get("tenant_name", "")
            
            # 创建组ID：对于单租户服务，客户+租户为一组；对于多租户服务，只按客户分组
            if customer_name == "多租户服务":
                group_id = f"multitenant_{job.get('service_name', 'unknown')}"
                group_name = f"多租户服务({job.get('service_name', 'unknown')})"
            else:
                group_id = f"{customer_name}_{tenant_name}" if tenant_name else customer_name
                group_name = f"{customer_name}({tenant_name})" if tenant_name else customer_name
            
            if group_id not in groups:
                groups[group_id] = ExecutionGroup(customer_name, tenant_name)
                groups[group_id].group_id = group_id
                groups[group_id].display_name = group_name
            
            groups[group_id].add_job(job)
        
        # 记录统计信息
        self.total_jobs = len(jobs)
        self.execution_log.append(f"按客户分组完成: {len(groups)} 个执行组，共 {self.total_jobs} 个Jobs")
        
        for group_id, group in groups.items():
            self.execution_log.append(
                f"  - {group.display_name}: {len(group.jobs)} 个Jobs (部署顺序: {[j.get('deployment_order', 999) for j in group.jobs]})"
            )
        
        return groups
    
    def start_execution(self, jobs: List[Dict]) -> Dict[str, any]:
        """开始执行管理"""
        try:
            # 重置状态
            self.execution_groups = {}
            self.job_status = {}
            self.job_results = {}
            self.execution_log = []
            self.completed_jobs = 0
            self.failed_jobs = 0
            self.running_jobs = 0
            
            # 按客户分组
            self.execution_groups = self.organize_jobs_by_customer(jobs)
            
            # 初始化所有Job状态
            for job in jobs:
                job_name = job.get("job_name", f"unknown_{len(self.job_status)}")
                self.job_status[job_name] = JobStatus.PENDING
                self.job_results[job_name] = {
                    "status": "PENDING",
                    "start_time": None,
                    "end_time": None,
                    "build_number": None,
                    "group_id": None,
                    "job_data": job
                }
            
            self.execution_log.append("执行管理器启动成功")
            return {
                "success": True,
                "message": "执行管理器启动成功",
                "groups": len(self.execution_groups),
                "total_jobs": self.total_jobs
            }
            
        except Exception as e:
            error_msg = f"启动执行管理器失败: {str(e)}"
            logger.error(error_msg)
            self.execution_log.append(error_msg)
            return {
                "success": False,
                "message": error_msg
            }
    
    def get_next_jobs_to_execute(self) -> List[Tuple[str, Dict]]:
        """获取下一批可以执行的Jobs (每个组最多一个)"""
        ready_jobs = []
        
        for group_id, group in self.execution_groups.items():
            if not group.is_group_completed():
                current_job = group.get_current_job()
                if current_job:
                    job_name = current_job.get("job_name")
                    current_status = self.job_status.get(job_name, JobStatus.PENDING)
                    
                    # 只有处于PENDING状态的Job可以启动
                    if current_status == JobStatus.PENDING:
                        ready_jobs.append((group_id, current_job))
                        
        return ready_jobs
    
    def trigger_job(self, group_id: str, job: Dict) -> Dict[str, any]:
        """触发单个Job执行"""
        job_name = job.get("job_name")
        
        try:
            self.execution_log.append(f"开始执行 {group_id} 组的 Job: {job_name}")
            
            # 更新状态
            self.job_status[job_name] = JobStatus.RUNNING
            self.job_results[job_name].update({
                "status": "RUNNING",
                "start_time": datetime.now(),
                "group_id": group_id
            })
            self.running_jobs += 1
            
            # 如果有Jenkins客户端，触发真实执行
            if self.jenkins_client:
                result = self.jenkins_client.trigger_job(
                    job_name, 
                    job.get("parameters", {})
                )
                
                if result["success"]:
                    self.job_results[job_name]["build_number"] = result.get("build_number")
                    self.execution_log.append(f"✅ Job {job_name} 触发成功，Build: {result.get('build_number')}")
                    return {
                        "success": True,
                        "job_name": job_name,
                        "build_number": result.get("build_number"),
                        "group_id": group_id
                    }
                else:
                    # 触发失败
                    self.mark_job_completed(job_name, False, result.get("message", "触发失败"))
                    return {
                        "success": False,
                        "job_name": job_name,
                        "message": result.get("message", "触发失败"),
                        "group_id": group_id
                    }
            else:
                # 模拟执行
                self.execution_log.append(f"🎭 模拟执行 Job {job_name}")
                return {
                    "success": True,
                    "job_name": job_name,
                    "build_number": 999,  # 模拟build号
                    "group_id": group_id,
                    "simulation": True
                }
                
        except Exception as e:
            error_msg = f"触发Job {job_name} 失败: {str(e)}"
            logger.error(error_msg)
            self.mark_job_completed(job_name, False, error_msg)
            return {
                "success": False,
                "job_name": job_name,
                "message": error_msg,
                "group_id": group_id
            }
    
    def mark_job_completed(self, job_name: str, success: bool = True, error_message: str = ""):
        """标记Job完成"""
        try:
            current_status = self.job_status.get(job_name)
            if current_status == JobStatus.RUNNING:
                self.running_jobs -= 1
            
            # 更新Job状态
            new_status = JobStatus.SUCCESS if success else JobStatus.FAILURE
            self.job_status[job_name] = new_status
            
            self.job_results[job_name].update({
                "status": new_status.value,
                "end_time": datetime.now(),
                "error_message": error_message if not success else ""
            })
            
            if success:
                self.completed_jobs += 1
                self.execution_log.append(f"✅ Job {job_name} 执行成功")
            else:
                self.failed_jobs += 1
                self.execution_log.append(f"❌ Job {job_name} 执行失败: {error_message}")
            
            # 查找并更新对应的执行组
            group_id = self.job_results[job_name].get("group_id")
            if group_id and group_id in self.execution_groups:
                group = self.execution_groups[group_id]
                # 如果是当前执行的Job，标记为完成并移动到下一个
                current_job = group.get_current_job()
                if current_job and current_job.get("job_name") == job_name:
                    group.mark_current_job_completed(success)
                    
                    if group.is_group_completed():
                        self.execution_log.append(f"🎉 执行组 {group.display_name} 全部完成")
                    else:
                        next_job = group.get_current_job()
                        if next_job:
                            self.execution_log.append(f"📋 {group.display_name} 准备执行下一个Job: {next_job.get('job_name')}")
            
        except Exception as e:
            error_msg = f"标记Job {job_name} 完成状态失败: {str(e)}"
            logger.error(error_msg)
            self.execution_log.append(error_msg)
    
    def check_job_status(self, job_name: str, build_number: int) -> Dict[str, any]:
        """检查Job状态"""
        try:
            if self.jenkins_client:
                status_result = self.jenkins_client.get_build_status(job_name, build_number)
                if status_result["success"]:
                    building = status_result.get("building", True)
                    status = status_result.get("status", "UNKNOWN")
                    
                    if not building:  # Job已完成
                        success = status == "SUCCESS"
                        self.mark_job_completed(job_name, success, f"Jenkins状态: {status}")
                        
                    return {
                        "success": True,
                        "building": building,
                        "status": status,
                        "job_name": job_name,
                        "build_number": build_number
                    }
                else:
                    return {
                        "success": False,
                        "message": status_result.get("message", "状态检查失败")
                    }
            else:
                # 模拟状态检查
                return {
                    "success": True,
                    "building": False,
                    "status": "SUCCESS",
                    "job_name": job_name,
                    "build_number": build_number,
                    "simulation": True
                }
                
        except Exception as e:
            error_msg = f"检查Job {job_name} 状态失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }
    
    def get_execution_summary(self) -> Dict[str, any]:
        """获取执行摘要"""
        pending_count = sum(1 for status in self.job_status.values() if status == JobStatus.PENDING)
        
        return {
            "total_jobs": self.total_jobs,
            "completed_jobs": self.completed_jobs,
            "failed_jobs": self.failed_jobs,
            "running_jobs": self.running_jobs,
            "pending_jobs": pending_count,
            "total_groups": len(self.execution_groups),
            "completed_groups": sum(1 for group in self.execution_groups.values() if group.is_group_completed()),
            "execution_log": self.execution_log,
            "all_completed": self.completed_jobs + self.failed_jobs == self.total_jobs
        }
    
    def get_group_status(self) -> Dict[str, Dict]:
        """获取各个执行组的状态"""
        group_status = {}
        
        for group_id, group in self.execution_groups.items():
            current_job = group.get_current_job()
            completed_jobs = group.get_completed_jobs()
            pending_jobs = group.get_pending_jobs()
            
            group_status[group_id] = {
                "display_name": getattr(group, 'display_name', group_id),
                "customer_name": group.customer_name,
                "tenant_name": group.tenant_name,
                "total_jobs": len(group.jobs),
                "completed_jobs": len(completed_jobs),
                "current_job": current_job.get("job_name") if current_job else None,
                "current_job_status": self.job_status.get(current_job.get("job_name")) if current_job else None,
                "pending_jobs": len(pending_jobs),
                "is_completed": group.is_group_completed(),
                "jobs_detail": [
                    {
                        "job_name": job.get("job_name"),
                        "deployment_order": job.get("deployment_order"),
                        "status": self.job_status.get(job.get("job_name"), JobStatus.PENDING).value
                    }
                    for job in group.jobs
                ]
            }
            
        return group_status
    
    def is_execution_completed(self) -> bool:
        """检查是否所有执行都已完成"""
        return self.completed_jobs + self.failed_jobs == self.total_jobs
    
    def get_ready_groups(self) -> List[str]:
        """获取有Jobs准备执行的组"""
        ready_groups = []
        for group_id, group in self.execution_groups.items():
            if not group.is_group_completed():
                current_job = group.get_current_job()
                if current_job:
                    job_name = current_job.get("job_name")
                    if self.job_status.get(job_name) == JobStatus.PENDING:
                        ready_groups.append(group_id)
        return ready_groups 