"""
常量定义文件
定义应用中使用的常量和配置
"""

# 应用配置
APP_TITLE = "智能发布助手"
APP_ICON = "✨"

# 工作流状态
WORKFLOW_STATES = {
    "START": "start",
    "PLAN_GENERATED": "plan_generated", 
    "PLAN_APPROVED": "plan_approved",
    "EXECUTION_READY": "execution_ready",
    "EXECUTING": "executing",
    "MONITORING": "monitoring",
    "COMPLETED": "completed",
    "ERROR": "error"
}

# Jenkins Job状态
JOB_STATUS = {
    "SUCCESS": "SUCCESS",
    "FAILURE": "FAILURE", 
    "RUNNING": "RUNNING",
    "ABORTED": "ABORTED",
    "UNKNOWN": "UNKNOWN"
}

# 状态颜色映射
STATUS_COLORS = {
    "SUCCESS": "green",
    "RUNNING": "blue", 
    "FAILURE": "red",
    "ABORTED": "orange",
    "UNKNOWN": "gray"
}

# 时间状态
TIME_STATUS = {
    "IN_TIME": "in_time",
    "FUTURE": "future",
    "PAST": "past", 
    "NO_TIME": "no_time"
}

# 默认配置
DEFAULT_CONFIG = {
    "JENKINS_TIMEOUT": 300,  # 5分钟
    "REFRESH_INTERVAL": 10,  # 10秒
    "MAX_LOG_LINES": 20,     # 最多显示20行日志
    "CACHE_DURATION": 300,   # 缓存5分钟
    "MAX_RECURSION_LIMIT": 50,
    "MAX_EXECUTION_TIME": 300
}

# UI配置
UI_CONFIG = {
    "SIDEBAR_WIDTH": 300,
    "CONTAINER_HEIGHT": 300,
    "PROGRESS_BAR_HEIGHT": 20
}

# 错误消息
ERROR_MESSAGES = {
    "NO_VERSION_ENV": "无法识别版本号和环境名，请使用格式如：'列出 25R1.2 Prod 的发布计划'",
    "NO_DEPLOYMENT_PLAN": "未找到发布计划数据",
    "NO_JENKINS_JOBS": "没有可用的 Jenkins Jobs",
    "NO_AGENT": "Agent未初始化",
    "DB_CONNECTION_FAILED": "数据库连接失败",
    "JENKINS_CONNECTION_FAILED": "Jenkins连接失败"
}

# 成功消息
SUCCESS_MESSAGES = {
    "PLAN_APPROVED": "发布计划已审批",
    "EXECUTION_STARTED": "Jenkins jobs已开始执行",
    "MONITORING_STARTED": "监控已启动",
    "JOB_COMPLETED": "Job执行完成"
}

# 版本号正则表达式模式
VERSION_PATTERNS = [
    r'版本[：:]?\s*(\d+R\d+\.\d+).*?环境[：:]?\s*(\w+)',  # 版本：25R1.2 环境：Prod
    r'环境[：:]?\s*(\w+).*?版本[：:]?\s*(\d+R\d+\.\d+)',  # 环境：Prod 版本：25R1.2
    r'版本[：:]?\s*(\d+R\d+\.\d+)\s+环境[：:]?\s*(\w+)',  # 版本：25R1.5 环境：Staging
    r'(\d+R\d+\.\d+)\s+(\w+)',  # 25R1.2 Prod (放在最后，避免误匹配)
]

# 环境列表
ENVIRONMENTS = ["Prod", "Staging", "Test", "Dev"]

# Tab标题
TAB_TITLES = [
    "📝 计划",
    "✅ 审批", 
    "🚀 执行",
    "📊 监控",
    "🌊 工作流"
]
